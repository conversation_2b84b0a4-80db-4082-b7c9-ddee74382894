package com.snails.common.widget

import android.annotation.SuppressLint
import android.content.Context
import android.os.CountDownTimer
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.appcompat.widget.AppCompatTextView
import androidx.appcompat.widget.LinearLayoutCompat
import com.blankj.utilcode.util.ColorUtils
import com.snails.base.utils.ext.gone

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年02月10日 14:55:57
 */
class TimerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayoutCompat(context, attrs, defStyleAttr) {

    private var hourText: AppCompatTextView
    private var minuteText: AppCompatTextView
    private var secondText: AppCompatTextView
    private var separator1: AppCompatTextView
    private var separator2: AppCompatTextView

    private var timeInMillis: Long = 0L  // 时间戳
    private val timerTimeOut = 3 * 60 * 60 * 1000L
    private var timer: CountDownTimer? = null

    @Volatile
    private var callTimerTimeOut = false //是否回调过即将超时回调

    init {
        // 使用 LayoutInflater 加载布局
        val layoutId = R.layout.layout_timer_view
        val view = LayoutInflater.from(context).inflate(layoutId, this, true)
        hourText = view.findViewById(R.id.hourText)
        minuteText = view.findViewById(R.id.minuteText)
        secondText = view.findViewById(R.id.secondText)
        separator1 = view.findViewById(R.id.separator1)
        separator2 = view.findViewById(R.id.separator2)
    }

    fun setShowMode(mode: Int) {
        when (mode) {
            1 -> {
                setColor(R.drawable.shape_tw_4r_tb2_lr4_bg, R.color.text_warn)
            }

            2 -> {
                setColor(R.drawable.shape_spb_4r_tb2_lr4_bg, R.color.surface_primary_button)
            }
        }
    }

    private fun setColor(@DrawableRes resId: Int, @ColorRes colorId: Int) {
        hourText.setBackgroundResource(resId)
        minuteText.setBackgroundResource(resId)
        secondText.setBackgroundResource(resId)
        separator1.setTextColor(ColorUtils.getColor(colorId))
        separator2.setTextColor(ColorUtils.getColor(colorId))
    }

    // 外部调用设置时间戳
    fun setTimeStamp(timeInMillis: Long, timeout: () -> Unit, complete: () -> Unit) {
        this.timeInMillis = timeInMillis
        startTimer(timeout, complete)
    }

    // 启动倒计时
    private fun startTimer(timeout: () -> Unit, complete: () -> Unit) {
        timer?.cancel()  // 取消之前的计时器

        timer = object : CountDownTimer(timeInMillis, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                timeInMillis = millisUntilFinished
                if (!callTimerTimeOut && timeInMillis <= timerTimeOut) {
                    timeout.invoke()
                    callTimerTimeOut = true
                }
                updateTimeDisplay()
            }

            override fun onFinish() {
                // 结束后的逻辑
                timeInMillis = 0
                gone()
                complete.invoke()
                timer?.cancel()
            }
        }
        timer?.start()
    }

    // 更新时间显示
    @SuppressLint("DefaultLocale")
    private fun updateTimeDisplay() {
        val hours = (timeInMillis / (1000 * 60 * 60)).toInt()
        val minutes = ((timeInMillis % (1000 * 60 * 60)) / (1000 * 60)).toInt()
        val seconds = ((timeInMillis % (1000 * 60)) / 1000).toInt()

        hourText.text = String.format("%02d", hours)
        minuteText.text = String.format("%02d", minutes)
        secondText.text = String.format("%02d", seconds)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        timer?.cancel()  // 停止计时器
    }
}