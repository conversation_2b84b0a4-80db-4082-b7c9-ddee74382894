package com.snails.common.widget

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.appcompat.widget.AppCompatCheckBox
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airbnb.lottie.LottieAnimationView
import com.google.android.material.imageview.ShapeableImageView
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.teacher.HomeworkDetailsItemInfo
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.common.widget.tagview.TagContainerLayout

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年01月14日 16:56:01
 */
class ChatItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private val rlyLeftContainer: RelativeLayout
    private val sivLeftHead: ShapeableImageView
    private val sivPic: ShapeableImageView
    private val clyContentInfo: LinearLayout
    private val tvProgress: AppCompatTextView
    private val tvTitle: AppCompatTextView
    private val tvTimeout: AppCompatTextView
    private val llyLeftAudio: LinearLayout
    private val lottieLeftLaBa: LottieAnimationView
    private val tvLeftDuration: AppCompatTextView
    private val cbSelect: AppCompatCheckBox

    private val clyRightContainer: ConstraintLayout
    private val sivRightHead: ShapeableImageView
    private val tvRightTxt: AppCompatTextView
    private val tvRightDuration: AppCompatTextView
    private val lottieRightLaBa: LottieAnimationView
    private val llyLeftContent: LinearLayout
    private val videoCover: ShapeableImageView
    private val flyVideo: FrameLayout
    private val tagContainerLayout: TagContainerLayout
    private val clyRightAudioContainer: ConstraintLayout
    private val clyRightPicVideoContainer: ConstraintLayout
    private val flyRightTxtContainer: FrameLayout
    private val sivRightPicVideo: ShapeableImageView
    private val ivRightVideoCover: AppCompatImageView

    init {
        // 使用 LayoutInflater 加载布局
        val view = LayoutInflater.from(context).inflate(R.layout.layout_chat_item_view, this, true)
        rlyLeftContainer = view.findViewById(R.id.rlyLeftContainer)
        sivLeftHead = view.findViewById(R.id.sivLeftHead)
        sivPic = view.findViewById(R.id.sivPic)
        clyContentInfo = view.findViewById(R.id.clyContentInfo)
        tvProgress = view.findViewById(R.id.tvProgress)
        tvTitle = view.findViewById(R.id.tvTitle)
        tvTimeout = view.findViewById(R.id.tvTimeout)
        llyLeftAudio = view.findViewById(R.id.llyLeftAudio)
        lottieLeftLaBa = view.findViewById(R.id.lottieLeftLaBa)
        cbSelect = view.findViewById(R.id.cbSelect)

        tvLeftDuration = view.findViewById(R.id.tvLeftDuration)
        clyRightContainer = view.findViewById(R.id.clyRightContainer)
        sivRightHead = view.findViewById(R.id.sivRightHead)
        tvRightTxt = view.findViewById(R.id.tvRightTxt)
        tvRightDuration = view.findViewById(R.id.tvRightDuration)
        lottieRightLaBa = view.findViewById(R.id.lottieRightLaBa)
        llyLeftContent = view.findViewById(R.id.llyLeftContent)
        videoCover = view.findViewById(R.id.videoCover)
        flyVideo = view.findViewById(R.id.flyVideo)
        tagContainerLayout = view.findViewById(R.id.tagContainerLayout)
        clyRightAudioContainer = view.findViewById(R.id.clyRightAudioContainer)
        flyRightTxtContainer = view.findViewById(R.id.flyRightTxtContainer)
        clyRightPicVideoContainer = view.findViewById(R.id.clyRightPicVideoContainer)
        sivRightPicVideo = view.findViewById(R.id.sivRightPicVideo)
        ivRightVideoCover = view.findViewById(R.id.ivRightVideoCover)
    }

    @SuppressLint("SetTextI18n")
    fun setData(info: HomeworkDetailsItemInfo, listener: ChatListener) {
        if (info.messageType == "STUDENT_COURSEWORK") { //学生
            rlyLeftContainer.visible()
            clyRightContainer.gone()
            sivLeftHead.load(info.avatar)
            when (info.courseworkType) {
                "STUDENT_SHOW_EXAM" -> { //表演输出
                    llyLeftContent.gone()
                    flyVideo.visible()
                    videoCover.load(info.videoCover)
                    flyVideo.singleClick {
                        HRouter.navigation(RouterPath.VIDEO_PLAY, Bundle().apply {
                            putString("playUrl", info.resource)
                            putString("videoCover", info.videoCover)
                            putBoolean("canDownload", true)
                        })
                    }
                }

                "DUBBING" -> {
                    sivPic.gone()
                    tvTitle.visible()
                    tvProgress.text = "${info.index}/${info.groupingSize ?: 0}"
                    tvTitle.text = "${info.originText}"
                    if (info.resource.isNullOrEmpty()) {
                        tvTimeout.visible()
                        llyLeftAudio.gone()
                    } else {
                        tvTimeout.gone()
                        llyLeftAudio.visible()
                        tvLeftDuration.text = showAudioLength(info.audioLength)
                        llyLeftAudio.singleClick {
                            //点击播放学生音频
                            listener.clickAudio(info, lottieLeftLaBa)
                        }
                    }
                }

                else -> {
                    llyLeftContent.visible()
                    flyVideo.gone()
                    tvProgress.text = "${info.index}/${info.groupingSize ?: 0}"
                    cbSelect.singleClick {
                        info.cbChecked = cbSelect.isChecked
                        listener.select(cbSelect, info)
                    }

                    when (info.courseworkType) {
                        "SELECT_VOCABULARY_EXAM" -> { //拼词成意
                            tvTitle.gone()
                            sivPic.gone()
                            tagContainerLayout.visible()
                            info.originSelectVocabulary?.let { tagContainerLayout.setTags(it) }
                        }

                        else -> {
                            sivPic.visible()
                            sivPic.load(
                                info.originResource,
                                placeholder = R.drawable.svg_w343_h137_placeholder,
                                error = R.drawable.svg_w343_h137_error
                            )
                            tvTitle.visible()
                            tvTitle.text = "${info.originText}"
                        }
                    }

                    if (info.resource.isNullOrEmpty()) {
                        tvTimeout.visible()
                        llyLeftAudio.gone()
                    } else {
                        tvTimeout.gone()
                        llyLeftAudio.visible()
                        tvLeftDuration.text = showAudioLength(info.audioLength)
                        llyLeftAudio.singleClick {
                            //点击播放学生音频
                            listener.clickAudio(info, lottieLeftLaBa)
                        }
                    }
                }
            }
            if (info.canAiCorrecting == true) {
                cbSelect.visible()
                cbSelect.isChecked = info.cbChecked
            } else {
                cbSelect.gone()
            }
        } else { //老师
            rlyLeftContainer.gone()
            clyRightContainer.visible()
            sivRightHead.load(info.avatar)
            when (info.resourceType) {
                "TEXT" -> {
                    flyRightTxtContainer.visible()
                    clyRightAudioContainer.gone()
                    clyRightPicVideoContainer.gone()
                    tvRightTxt.text = info.resource
                    flyRightTxtContainer.setOnLongClickListener {
                        listener.longClick(info)
                        false
                    }
                }

                "AUDIO" -> {
                    flyRightTxtContainer.gone()
                    clyRightPicVideoContainer.gone()
                    clyRightAudioContainer.visible()
                    tvRightDuration.text = showAudioLength(info.audioLength)
                    clyRightAudioContainer.singleClick {
                        //点击播放老师音频
                        listener.clickAudio(info, lottieRightLaBa)
                    }
                    clyRightAudioContainer.setOnLongClickListener {
                        listener.longClick(info)
                        false
                    }
                }

                "PIC", "VIDEO" -> {
                    flyRightTxtContainer.gone()
                    clyRightAudioContainer.gone()
                    clyRightPicVideoContainer.visible()
                    sivRightPicVideo.load(info.resource)
                    if (info.resourceType == "VIDEO") {
                        ivRightVideoCover.visible()
                    } else {
                        ivRightVideoCover.gone()
                    }
                    clyRightPicVideoContainer.setOnLongClickListener {
                        listener.longClick(info)
                        false
                    }
                    clyRightPicVideoContainer.singleClick {
                        if (info.resourceType == "VIDEO") {
                            //查看视频
                            HRouter.navigation(RouterPath.VIDEO_PLAY, Bundle().apply {
                                putString("playUrl", info.resource)
                                putString("videoCover", info.videoCover)
                            })
                        } else {
                            //查看大图
                            listener.lookLargeImage(info.resource)
                        }
                    }
                }
            }
        }
    }

    private fun showAudioLength(audioLength: Int?): String {
        if (audioLength == null || audioLength <= 0) {
            return "1″"
        }
        return "$audioLength″"
    }

    interface ChatListener {
        fun clickAudio(info: HomeworkDetailsItemInfo, laBa: LottieAnimationView)
        fun longClick(info: HomeworkDetailsItemInfo)
        fun lookLargeImage(imageUrl: String?)
        fun select(isChecked: AppCompatCheckBox, info: HomeworkDetailsItemInfo)
    }
}