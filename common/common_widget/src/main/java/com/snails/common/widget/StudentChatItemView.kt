package com.snails.common.widget

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.airbnb.lottie.LottieAnimationView
import com.google.android.material.imageview.ShapeableImageView
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.teacher.HomeworkDetailsItemInfo
import com.snails.base.router.HRouter
import com.snails.base.router.RouterPath
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.common.widget.tagview.TagContainerLayout

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年01月14日 16:56:01
 */
class StudentChatItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private val clyRightContainer: ConstraintLayout
    private val sivRightHead: ShapeableImageView
    private val flyRightVideo: ConstraintLayout
    private val videoRightCover: ShapeableImageView
    private val llyRightContent: LinearLayout
    private val sivRightPic: ShapeableImageView
    private val llyRightContentInfo: LinearLayout
    private val tvRightProgress: AppCompatTextView
    private val tvRightTitle: AppCompatTextView
    private val tagRightContainerLayout: TagContainerLayout
    private val tvRightTimeout: AppCompatTextView
    private val llyRightAudio: LinearLayout
    private val lottieRightLaBa: LottieAnimationView
    private val tvRightDuration: AppCompatTextView

    private val clyLeftContainer: ConstraintLayout
    private val sivLeftHead: ShapeableImageView
    private val flyLeftTxtContainer: FrameLayout
    private val tvLeftTxt: AppCompatTextView
    private val clyLeftAudioContainer: ConstraintLayout
    private val lottieLeftLaBa: LottieAnimationView
    private val tvLeftDuration: AppCompatTextView
    private val clyLeftPicVideoContainer: ConstraintLayout
    private val sivLeftPicVideo: ShapeableImageView
    private val ivLeftVideoCover: AppCompatImageView

    init {
        // 使用 LayoutInflater 加载布局
        val view =
            LayoutInflater.from(context).inflate(R.layout.layout_student_chat_item_view, this, true)
        //右边学生
        clyRightContainer = view.findViewById(R.id.clyRightContainer)
        sivRightHead = view.findViewById(R.id.sivRightHead)
        flyRightVideo = view.findViewById(R.id.flyRightVideo)
        videoRightCover = view.findViewById(R.id.videoRightCover)
        llyRightContent = view.findViewById(R.id.llyRightContent)
        sivRightPic = view.findViewById(R.id.sivRightPic)
        llyRightContentInfo = view.findViewById(R.id.llyRightContentInfo)
        tvRightProgress = view.findViewById(R.id.tvRightProgress)
        tvRightTitle = view.findViewById(R.id.tvRightTitle)
        tagRightContainerLayout = view.findViewById(R.id.tagRightContainerLayout)
        tvRightTimeout = view.findViewById(R.id.tvRightTimeout)
        llyRightAudio = view.findViewById(R.id.llyRightAudio)
        lottieRightLaBa = view.findViewById(R.id.lottieRightLaBa)
        tvRightDuration = view.findViewById(R.id.tvRightDuration)

        //左边老师
        clyLeftContainer = view.findViewById(R.id.clyLeftContainer)
        sivLeftHead = view.findViewById(R.id.sivLeftHead)
        flyLeftTxtContainer = view.findViewById(R.id.flyLeftTxtContainer)
        tvLeftTxt = view.findViewById(R.id.tvLeftTxt)
        clyLeftAudioContainer = view.findViewById(R.id.clyLeftAudioContainer)
        lottieLeftLaBa = view.findViewById(R.id.lottieLeftLaBa)
        tvLeftDuration = view.findViewById(R.id.tvLeftDuration)
        clyLeftPicVideoContainer = view.findViewById(R.id.clyLeftPicVideoContainer)
        sivLeftPicVideo = view.findViewById(R.id.sivLeftPicVideo)
        ivLeftVideoCover = view.findViewById(R.id.ivLeftVideoCover)
    }

    @SuppressLint("SetTextI18n")
    fun setData(info: HomeworkDetailsItemInfo, listener: ChatListener) {
        if (info.messageType == "STUDENT_COURSEWORK") { //学生
            clyLeftContainer.gone()
            clyRightContainer.visible()
            sivRightHead.load(info.avatar)
            when (info.courseworkType) {
                "STUDENT_SHOW_EXAM" -> { //表演输出
                    llyRightContent.gone()
                    flyRightVideo.visible()
                    videoRightCover.load(
                        info.videoCover,
                        placeholder = R.drawable.svg_video_placeholder,
                        error = R.drawable.svg_video_error
                    )
                    flyRightVideo.singleClick {
                        HRouter.navigation(RouterPath.VIDEO_PLAY, Bundle().apply {
                            putString("playUrl", info.resource)
                            putString("videoCover", info.videoCover)
//                            putBoolean("landscape", true)
                        })
                    }
                }

                else -> {
                    llyRightContent.visible()
                    flyRightVideo.gone()
                    tvRightProgress.text = "${info.index}/${info.groupingSize ?: 0}"

                    when (info.courseworkType) {
                        "SELECT_VOCABULARY_EXAM" -> { //拼词成意
                            tvRightTitle.gone()
                            sivRightPic.gone()
                            tagRightContainerLayout.visible()
                            info.originSelectVocabulary?.let { tagRightContainerLayout.setTags(it) }
                        }

                        else -> {
                            sivRightPic.visible()
                            sivRightPic.load(
                                info.originResource,
                                placeholder = R.drawable.svg_w343_h137_placeholder,
                                error = R.drawable.svg_w343_h137_error
                            )
                            tvRightTitle.visible()
                            tvRightTitle.text = "${info.originText}"
                        }
                    }

                    if (info.resource.isNullOrEmpty()) {
                        tvRightTimeout.visible()
                        llyRightAudio.gone()
                    } else {
                        tvRightTimeout.gone()
                        llyRightAudio.visible()
                        tvRightDuration.text = showAudioLength(info.audioLength)
                        llyRightAudio.singleClick {
                            //点击播放学生音频
                            listener.clickAudio(info, lottieRightLaBa)
                        }
                    }
                }
            }
        } else { //老师
            clyRightContainer.gone()
            clyLeftContainer.visible()
            sivLeftHead.load(info.avatar)
            when (info.resourceType) {
                "TEXT" -> {
                    flyLeftTxtContainer.visible()
                    clyLeftAudioContainer.gone()
                    clyLeftPicVideoContainer.gone()
                    tvLeftTxt.text = info.resource
                    flyLeftTxtContainer.setOnLongClickListener {
                        listener.longClick(info)
                        false
                    }
                }

                "AUDIO" -> {
                    flyLeftTxtContainer.gone()
                    clyLeftAudioContainer.visible()
                    clyLeftPicVideoContainer.gone()
                    tvLeftDuration.text = showAudioLength(info.audioLength)
                    clyLeftAudioContainer.singleClick {
                        //点击播放老师音频
                        listener.clickAudio(info, lottieLeftLaBa)
                    }
                }

                "VIDEO",
                "PIC" -> {
                    flyLeftTxtContainer.gone()
                    clyLeftAudioContainer.gone()
                    clyLeftPicVideoContainer.visible()
                    sivLeftPicVideo.load(info.resource)
                    if (info.resourceType == "VIDEO") {
                        ivLeftVideoCover.visible()
                    } else {
                        ivLeftVideoCover.gone()
                    }
                    clyLeftPicVideoContainer.singleClick {
                        if (info.resourceType == "VIDEO") {
                            //查看视频
                            HRouter.navigation(RouterPath.VIDEO_PLAY, Bundle().apply {
                                putString("playUrl", info.resource)
                                putString("videoCover", info.videoCover)
//                                putBoolean("landscape", true)
                            })
                        } else {
                            //查看大图
                            listener.lookLargeImage(info.resource)
                        }
                    }
                }
            }
        }
    }

    private fun showAudioLength(audioLength: Int?): String {
        if (audioLength == null || audioLength <= 0) {
            return "1″"
        }
        return "$audioLength″"
    }

    interface ChatListener {
        fun clickAudio(info: HomeworkDetailsItemInfo, laBa: LottieAnimationView)
        fun longClick(info: HomeworkDetailsItemInfo)
        fun lookLargeImage(imageUrl: String?)
    }
}