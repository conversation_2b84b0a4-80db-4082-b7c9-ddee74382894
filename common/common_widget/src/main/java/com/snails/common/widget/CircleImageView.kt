package com.snails.common.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年05月19日 11:39:15
 */
class CircleImageView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    private val path = Path()
    private val paint = Paint().apply {
        isAntiAlias = true // 启用抗锯齿
    }

    init {
        // 确保背景透明
        setBackgroundColor(0)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        // 强制宽高相等，确保圆形
        val size = measuredWidth.coerceAtMost(measuredHeight)
        setMeasuredDimension(size, size)
    }

    override fun onDraw(canvas: Canvas) {
        // 获取视图的宽高
        val width = width.toFloat()
        val height = height.toFloat()
        val radius = width / 2f

        // 重置 Path
        path.reset()
        // 创建圆形路径
        path.addCircle(radius, radius, radius, Path.Direction.CW)

        // 裁剪画布为圆形
        canvas.clipPath(path)

        // 调用父类的绘制方法，绘制图片
        super.onDraw(canvas)
    }
}