package com.snails.module.base.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.annotation.DrawableRes
import androidx.appcompat.widget.AppCompatImageView
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.visible
import com.snails.module.base.R

/**
 * @Description 资源类型View
 * <AUTHOR>
 * @CreateTime 2024年12月06日 10:17:00
 */
class ResTypeView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    private var ivResTypeView: AppCompatImageView

    init {
        val rId = R.layout.module_base_common_res_type_view_layout
        val view = LayoutInflater.from(context).inflate(rId, this, true)
        ivResTypeView = view.findViewById(R.id.ivResTypeView)
    }

    /**
     * @param type //PIC, AUDIO, VIDEO, PIC_BOOK, GAME
     */
    fun setResType(type: String?) {
        when (type) {
            "AUDIO" -> {
                ivResTypeView.showResAndVisible(R.drawable.svg_res_type_music)
            }

            "VIDEO" -> {
                ivResTypeView.showResAndVisible(R.drawable.svg_res_type_video)
            }

            "PIC_BOOK" -> {
                ivResTypeView.showResAndVisible(R.drawable.svg_res_type_book)
            }

            else -> {
                gone()
            }
        }
    }

    private fun AppCompatImageView.showResAndVisible(@DrawableRes resId: Int) {
        this.apply {
            setImageResource(resId)
            visible()
        }
    }
}