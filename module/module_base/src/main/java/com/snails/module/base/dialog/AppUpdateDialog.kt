package com.snails.module.base.dialog

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.LifecycleCoroutineScope
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.ThreadUtils
import com.blankj.utilcode.util.ToastUtils
import com.snails.base.dialog.BaseDialog
import com.snails.base.network.repository.downloadx.State
import com.snails.base.network.repository.downloadx.download
import com.snails.base.network.repository.info.setting.AppVersionInfo
import com.snails.base.utils.constants.AppConstants
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.databinding.ModuleBaseAppUpdateLayoutBinding
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年03月27日 15:15:12
 */
class AppUpdateDialog() : BaseDialog<ModuleBaseAppUpdateLayoutBinding>() {

    private var appVersionInfo: AppVersionInfo? = null
    private var next: ((String) -> Unit)? = null
    private var close: (() -> Unit)? = null
    private var lifecycleScope: LifecycleCoroutineScope? = null

    constructor(
        info: AppVersionInfo,
        close: () -> Unit,
        next: (String) -> Unit,
        lifecycleScope: LifecycleCoroutineScope
    ) : this() {
        this.appVersionInfo = info
        this.next = next
        this.close = close
        this.lifecycleScope = lifecycleScope
    }

    override fun initView() {
        super.initView()
        binding.apply {
            tvTitle.text = appVersionInfo?.title ?: ""
            tvDescTxt.text = appVersionInfo?.detail ?: ""
            if (appVersionInfo?.force == true) {
                tvClose.gone()
            } else {
                tvClose.visible()
            }
        }
    }

    override fun initClick() {
        super.initClick()
        binding.apply {
            tvClose.singleClick {
                close?.invoke()
                dismiss()
            }
            tvUpdate.singleClick {
                tvClose.gone()
                tvUpdate.gone()
                binding.numberProgressBar.visible()
                binding.tvDownloadProgress.visible()
                binding.tvTips.visible()
                appVersionInfo?.downloadUrl?.let { url ->
                    lifecycleScope?.let { scope ->
                        val filePath = AppConstants.getDownloadPath()
                        val fileName = FileUtils.getFileName(url)
                        // 创建下载任务
                        scope.download(url, saveName = fileName, savePath = filePath).apply {
                            state().onEach { state ->
                                when (state) {
                                    is State.Succeed -> {
                                        next?.invoke("$filePath/$fileName")
                                        showBtn()
                                    }

                                    is State.Downloading -> {

                                    }

                                    is State.Failed -> {
                                        showBtn()
                                        showDownloadFailedTips()
                                    }

                                    is State.None -> {

                                    }

                                    is State.Stopped -> {

                                    }

                                    is State.Waiting -> {

                                    }
                                }
                                updateTxt(state.progress.percent().toInt())
                            }.launchIn(scope)
                        }
                    }
                }
            }
        }
    }

    private fun showDownloadFailedTips() {
        ThreadUtils.runOnUiThread {
            ToastUtils.showShort("下载失败，请稍后重试")
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateTxt(progress: Int) {
        try {
            binding.numberProgressBar.progress = progress
            binding.tvDownloadProgress.text = "${progress}%"
        } catch (t: Throwable) {
            showBtn()
        }
    }

    private fun showBtn() {
        ThreadUtils.runOnUiThread {
            if (appVersionInfo?.force != true) {
                binding.tvClose.visible()
            }
            binding.tvUpdate.visible()

            binding.numberProgressBar.gone()
            binding.tvDownloadProgress.gone()
            binding.tvTips.gone()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        dialog?.apply {
            setCanceledOnTouchOutside(false) //点击屏幕不消失
            setOnKeyListener(DialogInterface.OnKeyListener { _, keyCode, _ ->
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    return@OnKeyListener true
                }
                false
            })
        }
        return super.onCreateView(inflater, container, savedInstanceState)
    }
}