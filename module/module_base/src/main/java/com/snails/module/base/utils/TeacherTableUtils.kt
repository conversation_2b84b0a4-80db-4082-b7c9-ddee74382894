package com.snails.module.base.utils

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.os.Environment
import android.provider.MediaStore
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.graphics.createBitmap
import androidx.core.net.toUri
import com.snails.module.base.R
import com.snails.base.network.repository.info.teacher.SubmitItem
import com.snails.base.utils.ext.getRealPath
import com.snails.common.widget.CircleImageView
import java.io.OutputStream

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年05月19日 12:28:31
 */
object TeacherTableUtils {

    fun generateTablePhoto(
        context: Context,
        date: String,
        className: String,
        studentList: List<SubmitItem> = emptyList()
    ) {
        // 创建一个打卡表视图
        createCheckInTableView(
            context,
            date,
            className,
            studentList
        ).apply {
            // 将视图转换为位图
            val bitmap = viewToBitmap(context, this)
            // 保存位图到相册
            saveBitmapToGallery(context, bitmap, "蜗牛阅读打卡表_${date}_${className}")
        }
    }

    /**
     * 创建打卡表视图
     */
    private fun createCheckInTableView(
        context: Context,
        date: String,
        className: String,
        studentList: List<SubmitItem> = emptyList()
    ): View {
        // 创建一个垂直布局作为根视图
        val rootLayout = LinearLayout(context)
        rootLayout.orientation = LinearLayout.VERTICAL
        rootLayout.layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )

        // 1. 创建头部布局(包含头部图片和信息)
        val headerContainer = FrameLayout(context)
        headerContainer.layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )

        // 1.1 添加头部图片
        val headerImageView = ImageView(context)
        headerImageView.setImageResource(R.drawable.svg_check_in_table_header)
        headerImageView.scaleType = ImageView.ScaleType.FIT_XY
        headerImageView.layoutParams = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.WRAP_CONTENT
        )
        headerContainer.addView(headerImageView)

        // 1.2 添加信息内容覆盖在头部图片上
        val infoLayout = LinearLayout(context)
        infoLayout.orientation = LinearLayout.VERTICAL
        infoLayout.layoutParams = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.WRAP_CONTENT
        )
        infoLayout.setPadding(
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_12),
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_12),
            0,
            0
        )

        // 日期信息
        val dateLayout = LinearLayout(context)
        dateLayout.orientation = LinearLayout.HORIZONTAL
        dateLayout.gravity = android.view.Gravity.CENTER_VERTICAL
        dateLayout.layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        dateLayout.setPadding(
            0,
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_70),
            0,
            0
        )

        // 日期文本
        val dateTextView = TextView(context)
        dateTextView.text = date
        dateTextView.textSize = 14f
        dateTextView.setTextColor(Color.WHITE)
        dateTextView.setCompoundDrawablesWithIntrinsicBounds(R.drawable.svg_white_clock, 0, 0, 0)
        dateTextView.compoundDrawablePadding =
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_4)
        dateLayout.addView(dateTextView)

        infoLayout.addView(dateLayout)

        // 班级信息
        val classLayout = LinearLayout(context)
        classLayout.orientation = LinearLayout.HORIZONTAL
        classLayout.gravity = android.view.Gravity.CENTER_VERTICAL
        classLayout.layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        classLayout.setPadding(
            0,
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8),
            0,
            0
        )

        // 班级文本
        val classTextView = TextView(context)
        classTextView.text = className
        classTextView.textSize = 14f
        classTextView.setTextColor(Color.WHITE)
        classTextView.setCompoundDrawablesWithIntrinsicBounds(R.drawable.svg_group, 0, 0, 0)
        classTextView.compoundDrawablePadding =
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_4)
        classLayout.addView(classTextView)

        infoLayout.addView(classLayout)
        headerContainer.addView(infoLayout)
        rootLayout.addView(headerContainer)

        // 2. 添加白色背景的卡片区域
        val cardView = LinearLayout(context)
        cardView.orientation = LinearLayout.VERTICAL
        val cardParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        cardView.layoutParams = cardParams
        cardView.setBackgroundColor(Color.WHITE)
        //int left, int top, int right, int bottom
        cardView.setPadding(
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_12),
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_16),
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_12),
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_16)
        )

        // 添加学生列表标题
        val listTitleLayout = LinearLayout(context)
        listTitleLayout.orientation = LinearLayout.HORIZONTAL
        listTitleLayout.layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )

        // 添加学生列表数据
        if (studentList.isNotEmpty()) {
            // 使用实际的学生列表数据
            studentList.forEachIndexed { index, student ->
                // 添加分割线（除了第一个项目前）
                if (index > 0) {
                    val divider = View(context)
                    val dividerParams = LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        1
                    )
                    dividerParams.topMargin =
                        context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8)
                    dividerParams.bottomMargin =
                        context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8)
                    divider.layoutParams = dividerParams
                    divider.setBackgroundColor(
                        ContextCompat.getColor(
                            context,
                            R.color.border_divider
                        )
                    )
                    cardView.addView(divider)
                }

                val studentItem = createStudentItem(
                    context,
                    student.studentAvatar?.getRealPath(),
                    student.studentName ?: "",
                    student.className ?: "",
                    student.hasSubmitted == true
                )
                cardView.addView(studentItem)
            }
        }
        // 添加白色卡片到根布局
        rootLayout.addView(cardView)

        // 3. 添加底部图片
        val footerImageView = ImageView(context)
        footerImageView.setImageResource(R.drawable.svg_check_in_table_footer)
        footerImageView.scaleType = ImageView.ScaleType.FIT_XY
        val footerParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        footerImageView.layoutParams = footerParams
        rootLayout.addView(footerImageView)
        return rootLayout
    }

    /**
     * 创建学生列表项
     */
    private fun createStudentItem(
        context: Context,
        studentAvatar: String?,
        name: String,
        className: String,
        isChecked: Boolean
    ): LinearLayout {
        val itemLayout = LinearLayout(context)
        itemLayout.orientation = LinearLayout.HORIZONTAL
        itemLayout.layoutParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        itemLayout.setPadding(
            0,
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8),
            0,
            context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8)
        )

        // 学生头像
        val avatarLayout = LinearLayout(context).apply {
            gravity = android.view.Gravity.CENTER_VERTICAL
            layoutParams = LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 4f)
            setBackgroundColor(Color.TRANSPARENT) // 确保父布局透明
        }

        val headSize = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_24)
        val avatarImage = CircleImageView(context).apply {
            layoutParams = LinearLayout.LayoutParams(headSize, headSize)
            setImageURI(studentAvatar?.toUri())
            scaleType = ImageView.ScaleType.FIT_CENTER // 避免拉伸填充
            setBackgroundColor(Color.TRANSPARENT) // 确保背景透明
        }
        avatarLayout.addView(avatarImage)

        // 学生姓名
        val nameText = TextView(context)
        nameText.text = name
        nameText.textSize = 14f
        nameText.setTextColor(ContextCompat.getColor(context, R.color.text_headline))
        nameText.maxLines = 2
        nameText.ellipsize = android.text.TextUtils.TruncateAt.END
        nameText.setPadding(context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_4), 0, 0, 0)
        avatarLayout.addView(nameText)

        itemLayout.addView(avatarLayout)

        // 班级信息
        val classText = TextView(context)
        classText.text = className
        classText.textSize = 14f
        classText.gravity = android.view.Gravity.CENTER
        classText.setTextColor(ContextCompat.getColor(context, R.color.text_describe))
        classText.maxLines = 2
        classText.ellipsize = android.text.TextUtils.TruncateAt.END
        classText.layoutParams =
            LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 5f)
        itemLayout.addView(classText)

        // 打卡状态
        val statusLayout = LinearLayout(context)
        statusLayout.gravity = android.view.Gravity.END or android.view.Gravity.CENTER_VERTICAL
        statusLayout.layoutParams =
            LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 2.5f)

        val statusText = TextView(context)
        statusText.text = if (isChecked) "已打卡" else "未打卡"
        statusText.maxLines = 1
        statusText.textSize = 12f

        // 设置状态文本的背景和文本颜色
        if (isChecked) {
            // 已打卡 - 绿色背景和文本
            statusText.setTextColor(ContextCompat.getColor(context, R.color.text_success))
            statusText.setBackgroundResource(R.drawable.shape_sb_4r_tb4_lr8_bg)
            statusText.setPadding(
                context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8),
                context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_2),
                context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8),
                context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_2)
            )
        } else {
            // 未打卡 - 红色背景和文本
            statusText.setTextColor(ContextCompat.getColor(context, R.color.text_error))
            statusText.setBackgroundResource(R.drawable.shape_ser_4r_tb4_lr8_bg)
            statusText.setPadding(
                context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8),
                context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_2),
                context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8),
                context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_2)
            )
        }

        statusLayout.addView(statusText)
        itemLayout.addView(statusLayout)

        return itemLayout
    }

    /**
     * 将视图转换为位图
     */
    private fun viewToBitmap(context: Context, view: View): Bitmap {
        // 测量并布局视图
        view.measure(
            View.MeasureSpec.makeMeasureSpec(
                context.resources.displayMetrics.widthPixels,
                View.MeasureSpec.EXACTLY
            ),
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        )
        view.layout(0, 0, view.measuredWidth, view.measuredHeight)

        // 创建位图并绘制视图
        val bitmap = createBitmap(view.measuredWidth, view.measuredHeight)
        val canvas = Canvas(bitmap)
        view.draw(canvas)

        return bitmap
    }

    /**
     * 保存位图到相册
     */
    private fun saveBitmapToGallery(context: Context, bitmap: Bitmap, fileName: String) {
        try {
            var outputStream: OutputStream? = null
            val contentValues = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, "$fileName.png")
                put(MediaStore.Images.Media.MIME_TYPE, "image/png")
                put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES)
            }

            val uri = context.contentResolver.insert(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                contentValues
            )

            uri?.let {
                outputStream = context.contentResolver.openOutputStream(it)
            }

            outputStream?.use {
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, it)
                Toast.makeText(context, "打卡表已保存到相册", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(context, "保存失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }
}