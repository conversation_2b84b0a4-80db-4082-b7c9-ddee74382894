package com.snails.module.base.dialog

import com.snails.base.dialog.BaseDialog
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.databinding.ModuleBaseRetryDiaogLayoutBinding

/**
 * @Description 重试弹窗
 * <AUTHOR>
 * @CreateTime 2025年02月27日 19:07:13
 */
class RetryDialog() : BaseDialog<ModuleBaseRetryDiaogLayoutBinding>() {

    private var retry: (() -> Unit)? = null

    constructor(retry: () -> Unit) : this() {
        this.retry = retry
    }

    override fun initClick() {
        super.initClick()
        binding.tvClose.singleClick {
            dismiss()
        }
        binding.tvRetry.singleClick {
            retry?.invoke()
            dismiss()
        }
    }
}