package com.snails.module.base.dialog

import android.graphics.Typeface
import androidx.appcompat.widget.AppCompatTextView
import com.blankj.utilcode.util.ColorUtils
import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.R
import com.snails.module.base.databinding.ModuleBaseSpeedDiaogLayoutBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年04月15日 11:50:15
 */
class SpeedBottomDialog() : BaseBottomDialog<ModuleBaseSpeedDiaogLayoutBinding>(heightDimen = 164) {

    private val list = mutableListOf<AppCompatTextView>()
    private var speed: String? = null
    private var selectSpeed: ((Float) -> Unit)? = null

    constructor(speed: String?, selectSpeed: (Float) -> Unit) : this() {
        this.speed = speed
        this.selectSpeed = selectSpeed
    }

    override fun initView() {
        super.initView()
        list.clear()
        list.add(binding.tvSpeedOne)
        list.add(binding.tvSpeedTwo)
        list.add(binding.tvSpeedThree)
        setCurrentSpeed(speed)
    }

    override fun initClick() {
        super.initClick()
        binding.tvCancel.singleClick {
            dismiss()
        }

        list.forEachIndexed { index, v ->
            v.singleClick {
                selectSpeed?.invoke(convert(index))
                dismiss()
            }
        }
    }

    private fun convert(index: Int): Float {
        return when (index) {
            0 -> 1.0f
            1 -> 1.5f
            else -> 2.0f
        }
    }

    private fun setCurrentSpeed(speed: String?) {
        list.forEach { v ->
            if (v.text == speed) {
                v.setBackgroundResource(R.drawable.shape_spb_13r_bg)
                v.setTextColor(ColorUtils.getColor(R.color.text_on_primary_button))
                v.setTypeface(null, Typeface.BOLD)
            } else {
                v.setBackgroundResource(R.drawable.shape_sb_13r_bg)
                v.setTextColor(ColorUtils.getColor(R.color.text_body))
                v.setTypeface(null, Typeface.NORMAL)
            }
        }
    }
}