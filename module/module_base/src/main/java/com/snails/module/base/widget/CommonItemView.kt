package com.snails.module.base.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.StringUtils
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.R

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月16日 12:08:40
 */
class CommonItemView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val ivItemIcon: AppCompatImageView
    private val tvItemTitle: AppCompatTextView
    private val ivItemRightIcon: AppCompatImageView
    private val tvItemRightTitle: AppCompatTextView
    private val itemView: View

    init {
        // 使用 LayoutInflater 加载布局
        val layoutId = R.layout.module_base_common_item_view_layout
        itemView = LayoutInflater.from(context).inflate(layoutId, this, true)

        ivItemIcon = itemView.findViewById(R.id.ivItemIcon)
        tvItemTitle = itemView.findViewById(R.id.tvItemTitle)
        ivItemRightIcon = itemView.findViewById(R.id.ivItemRightIcon)
        tvItemRightTitle = itemView.findViewById(R.id.tvItemRightTitle)

        // 读取自定义属性（如果有）
        attrs?.let {
            val ta = context.obtainStyledAttributes(it, R.styleable.CommonItemView, 0, 0)

            val itemIcon = ta.getResourceId(R.styleable.CommonItemView_itemIcon, 0)
            val itemTitle = ta.getString(R.styleable.CommonItemView_itemTitle) ?: ""
            val itemRightIcon = ta.getResourceId(
                R.styleable.CommonItemView_itemRightIcon,
                R.drawable.svg_arrow_right
            )

            setItemIcon(itemIcon)
            setItemTitle(itemTitle)
            setItemRightIcon(itemRightIcon)

            ta.recycle()
        }
    }

    // 设置文字
    fun setItemTitle(title: String) {
        tvItemTitle.text = title
    }

    // 设置左边的图标
    fun setItemIcon(id: Int) {
        if (id != 0) {
            ivItemIcon.apply {
                visible()
                setImageResource(id)
            }
        } else {
            ivItemIcon.gone()
        }
    }

    // 设置右边的图标
    fun setItemRightIcon(id: Int) {
        ivItemRightIcon.setImageResource(id)
    }

    // 设置右边的标题
    fun setItemRightTitle(title: String?) {
        if (title.isNullOrEmpty()) {
            tvItemRightTitle.text = StringUtils.getString(R.string.str_choose)
            tvItemRightTitle.setTextColor(ColorUtils.getColor(R.color.text_disable))
        } else {
            tvItemRightTitle.text = title
            tvItemRightTitle.setTextColor(ColorUtils.getColor(R.color.text_describe))
        }
    }

    // 设置Item点击事件
    fun setBackClickListener(listener: OnClickListener) {
        itemView.singleClick(listener)
    }
}