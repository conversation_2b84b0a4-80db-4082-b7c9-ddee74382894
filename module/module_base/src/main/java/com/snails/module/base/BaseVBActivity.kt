package com.snails.module.base

import android.os.Bundle
import android.view.LayoutInflater
import androidx.viewbinding.ViewBinding
import java.lang.reflect.ParameterizedType

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月12日 14:43:04
 */
open class BaseVBActivity<VB : ViewBinding> : BaseActivity() {

    protected lateinit var binding: VB

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = createBinding()
        beforeSetContentView()
        setContentView(binding.root)
        initData()
        initView()
        initClick()
        initObserve()
    }

    @Suppress("UNCHECKED_CAST")
    private fun createBinding(): VB {
        val vbClass = getVBClass()
        val inflateMethod = vbClass.getMethod("inflate", LayoutInflater::class.java)
        return inflateMethod.invoke(null, layoutInflater) as VB
    }

    @Suppress("UNCHECKED_CAST")
    private fun getVBClass(): Class<VB> {
        val type = javaClass.genericSuperclass as ParameterizedType
        val vbClass = type.actualTypeArguments[0] as Class<VB>
        return vbClass
    }
    open fun beforeSetContentView(){}
    open fun initData() {}
    open fun initView() {}
    open fun initObserve() {}
    open fun initClick() {}
}