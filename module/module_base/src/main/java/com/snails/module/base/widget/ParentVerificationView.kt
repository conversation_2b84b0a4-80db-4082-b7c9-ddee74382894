package com.snails.module.base.widget

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import com.blankj.utilcode.util.ImageUtils
import com.snails.module.base.R
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.pow
import kotlin.math.sin
import kotlin.math.sqrt

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年12月25日 10:15:12
 */
class ParentVerificationView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : View(context, attrs) {

    private val circlePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val progressPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val thumbPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val dashPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var circleRadius = 0f

    private var ringWidth = 20f //圆环宽度
    private var ringColor = Color.LTGRAY //圆环颜色
    private var thumbBitmap: Bitmap? = null //滑块图片
    private var thumbSize = 150f //滑块大小

    private var arrowBitmap: Bitmap? = null //滑块图片
    private var arrowHeight = 150f //滑块大小
    private var arrowWidth = 150f //滑块大小
    private var dashColor = Color.LTGRAY
    private var dashLength = 0f
    private var dashWidth = 0f
    private var dashSpace = 0f

    private var clockwise = true //是否顺时针滑动
    private var progressColor = Color.BLUE //进度颜色
    private var rectF = RectF() // 圆弧区域
    private var currentAngle = 0f //当前角度
    private var maxAngle = 360f
    private var onCompleteListener: (() -> Unit)? = null
    private var isDragging = false
    private var lastAngle = 0f

    // 绘制虚线段（支持顺时针和逆时针）
    private val quarterSweepAngle = -90f // 四分之一圆弧角度

    init {
        // 读取自定义属性（如果有）
        attrs?.let {
            val ta = context.obtainStyledAttributes(it, R.styleable.ParentVerificationView, 0, 0)
            //顺时针，逆时针
            clockwise = ta.getInt(R.styleable.ParentVerificationView_pvv_direction, 0) == 0
            ringWidth = ta.getDimension(R.styleable.ParentVerificationView_pvv_ringWidth, 0f)
            ringColor = ta.getColor(R.styleable.ParentVerificationView_pvv_ringColor, Color.LTGRAY)
            progressColor =
                ta.getColor(R.styleable.ParentVerificationView_pvv_progressColor, Color.BLUE)

            thumbSize = ta.getDimension(R.styleable.ParentVerificationView_pvv_thumbSize, 150f)
            val thumbBitmapId = ta.getInt(
                R.styleable.ParentVerificationView_pvv_thumbImage,
                R.drawable.ic_parent_verification_thumb
            )
            val bitmap = ImageUtils.getBitmap(thumbBitmapId)
            thumbBitmap =
                Bitmap.createScaledBitmap(bitmap, thumbSize.toInt(), thumbSize.toInt(), true)

            arrowHeight = ta.getDimension(R.styleable.ParentVerificationView_pvv_arrowHeight, 0f)
            arrowWidth = ta.getDimension(R.styleable.ParentVerificationView_pvv_arrowWidth, 0f)
            val arrowBitmapId = ta.getInt(
                R.styleable.ParentVerificationView_pvv_arrowImage,
                R.drawable.svg_arrow_down
            )
            val arrowBitmapTemp = ImageUtils.getBitmap(arrowBitmapId)
            arrowBitmap = Bitmap.createScaledBitmap(
                arrowBitmapTemp,
                arrowWidth.toInt(),
                arrowHeight.toInt(),
                true
            )

            dashColor = ta.getColor(R.styleable.ParentVerificationView_pvv_dashColor, Color.LTGRAY)
            dashLength = ta.getDimension(R.styleable.ParentVerificationView_pvv_dashLength, 0f)
            dashWidth = ta.getDimension(R.styleable.ParentVerificationView_pvv_dashWidth, 0f)
            dashSpace = ta.getDimension(R.styleable.ParentVerificationView_pvv_dashSpace, 0f)

            ta.recycle()
        }

        // 初始化画笔
        circlePaint.style = Paint.Style.STROKE
        circlePaint.strokeWidth = ringWidth
        circlePaint.color = ringColor

        progressPaint.style = Paint.Style.STROKE
        progressPaint.strokeWidth = ringWidth
        progressPaint.color = progressColor

        thumbPaint.style = Paint.Style.FILL

        dashPaint.apply {
            style = Paint.Style.STROKE
            strokeWidth = dashWidth
            color = dashColor // 虚线颜色
            pathEffect = DashPathEffect(floatArrayOf(dashLength, dashSpace), 0f) // 虚线样式
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val size = MeasureSpec.getSize(widthMeasureSpec)
            .coerceAtMost(MeasureSpec.getSize(heightMeasureSpec))
        setMeasuredDimension(size, size)
        circleRadius = (size - (thumbSize - ringWidth) * 2) / 2f
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        //计算中心点
        val centerX = width / 2f
        val centerY = height / 2f

        // 绘制背景圆环
        canvas.drawCircle(centerX, centerY, circleRadius, circlePaint)

        // 根据方向，绘制滑动路径
        val sweepAngle = if (clockwise) {
            currentAngle
        } else {
            -currentAngle
        }

        // 更新圆弧区域
        rectF.set(
            centerX - circleRadius,
            centerY - circleRadius,
            centerX + circleRadius,
            centerY + circleRadius
        )

        val sweepAngleArrow = if (clockwise) 90f else -90f // 逆时针时为负角度
        //绘制线段
        canvas.drawArc(rectF, -90f, sweepAngleArrow, false, dashPaint)
        // 计算结束点的角度（顺时针或逆时针）
        val endAngle = quarterSweepAngle + sweepAngleArrow
        // 绘制结束位置的图片
        drawImageAtEnd(canvas, centerX, centerY, circleRadius, endAngle)

        //绘制进度
        canvas.drawArc(rectF, -90f, sweepAngle, false, progressPaint)
        // 绘制滑块
        drawThumb(centerX, sweepAngle, centerY, canvas)
    }

    private fun drawThumb(
        centerX: Float,
        sweepAngle: Float,
        centerY: Float,
        canvas: Canvas
    ) {
        thumbBitmap?.let { bitmap ->
            val thumbX =
                centerX + circleRadius * cos(Math.toRadians((sweepAngle - 90).toDouble())).toFloat()
            val thumbY =
                centerY + circleRadius * sin(Math.toRadians((sweepAngle - 90).toDouble())).toFloat()
            canvas.drawBitmap(
                bitmap,
                thumbX - thumbSize / 2,
                thumbY - thumbSize / 2,
                thumbPaint
            )
        }
    }

    /**
     * 绘制虚线段结束时的箭头
     */
    private fun drawImageAtEnd(
        canvas: Canvas,
        centerX: Float,
        centerY: Float,
        radius: Float,
        angle: Float
    ) {
        // 计算结束点的坐标
        val endX = centerX + radius * cos(Math.toRadians(angle.toDouble())).toFloat()
        val endY = centerY + radius * sin(Math.toRadians(angle.toDouble())).toFloat()

        // 调整图片位置，使图片中心对齐结束点
        val left = endX - arrowWidth / 2
        val top = endY - arrowHeight / 2

        // 绘制图片
        arrowBitmap?.let { canvas.drawBitmap(it, left, top, null) }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        val centerX = width / 2f
        val centerY = height / 2f
        val touchX = event.x
        val touchY = event.y

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 检测是否点中滑块
                val touch = isTouchOnThumb(touchX, touchY)
                if (touch) {
                    isDragging = true
                    lastAngle = calculateAngle(centerX, centerY, touchX, touchY)
                    return true
                }
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDragging) {
                    val angle = calculateAngle(centerX, centerY, touchX, touchY)
                    val deltaAngle = (angle - lastAngle + 360) % 360
                    val reverseDeltaAngle = (lastAngle - angle + 360) % 360
                    // 判断滑动方向是否正确
                    val isCorrectDirection = if (clockwise) {
                        deltaAngle < 180 // 顺时针有效
                    } else {
                        reverseDeltaAngle < 180 // 逆时针有效
                    }
                    if (isCorrectDirection) {
                        // 更新 currentAngle，根据方向调整增量
                        currentAngle = if (clockwise) {
                            currentAngle + deltaAngle
                        } else {
                            currentAngle + reverseDeltaAngle // 逆时针增加
                        }
                        // 限制角度范围
                        currentAngle = currentAngle.coerceIn(0f, maxAngle)
                        lastAngle = angle
                        invalidate()
                    } else {
                        // 如果方向错误，回弹到当前角度
                        animateToStart()
                    }
                    return true
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (isDragging) {
                    isDragging = false
                    if (currentAngle >= maxAngle) {
                        onCompleteListener?.invoke()
                    } else {
                        animateToStart()
                    }
                    return true
                }
            }
        }
        return super.onTouchEvent(event)
    }

    private fun isTouchOnThumb(touchX: Float, touchY: Float): Boolean {
        thumbBitmap?.let {
            val centerX = width / 2f
            val centerY = height / 2f
            val thumbX =
                centerX + circleRadius * cos(Math.toRadians((currentAngle - 90).toDouble())).toFloat()
            val thumbY =
                centerY + circleRadius * sin(Math.toRadians((currentAngle - 90).toDouble())).toFloat()
            val distance = sqrt((touchX - thumbX).pow(2) + (touchY - thumbY).pow(2))
            return distance <= thumbSize / 2
        }
        return false
    }

    private fun calculateAngle(
        centerX: Float,
        centerY: Float,
        touchX: Float,
        touchY: Float
    ): Float {
        val dx = touchX - centerX
        val dy = touchY - centerY
        var angle = Math.toDegrees(atan2(dy.toDouble(), dx.toDouble())).toFloat() + 90f
        if (angle < 0) angle += 360f
        return angle
    }

    private fun animateToStart() {
        val animator = ValueAnimator.ofFloat(currentAngle, 0f)
        animator.duration = 300
        animator.addUpdateListener {
            currentAngle = it.animatedValue as Float
            invalidate()
        }
        animator.start()
    }

    fun setThumbBitmap(bitmap: Bitmap) {
        thumbBitmap = bitmap
        invalidate()
    }

    fun setThumbSize(width: Float) {
        thumbSize = width
        invalidate()
    }

    fun setOnCompleteListener(listener: () -> Unit) {
        onCompleteListener = listener
    }

    fun setClockwiseDirection(isClockwise: Boolean) {
        clockwise = isClockwise
    }
}