package com.snails.module.base.dialog

import com.blankj.utilcode.util.StringUtils
import com.snails.base.dialog.BaseDialog
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.R
import com.snails.module.base.bean.PermissionScene
import com.snails.module.base.databinding.ModuleBasePermissionDescLayoutBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年03月17日 16:14:35
 */
class PermissionDescDialog() : BaseDialog<ModuleBasePermissionDescLayoutBinding>() {

    private var scene: PermissionScene? = null
    private var next: (() -> Unit)? = null

    constructor(scene: PermissionScene, next: () -> Unit) : this() {
        this.scene = scene
        this.next = next
    }

    override fun initView() {
        super.initView()
        when (scene) {
            PermissionScene.RECORD -> {
                binding.tvDescTxt.text = StringUtils.getString(R.string.str_permission_record)
            }

            PermissionScene.CAMERA -> {
                binding.tvDescTxt.text = StringUtils.getString(R.string.str_permission_camera)
            }

            PermissionScene.PHOTO -> {
                binding.tvDescTxt.text = StringUtils.getString(R.string.str_permission_photo)
            }

            else -> {}
        }
    }

    override fun initClick() {
        super.initClick()
        binding.apply {
            tvKnow.singleClick {
                next?.invoke()
                dismiss()
            }
        }
    }
}