package com.snails.module.base.dialog

import android.app.Dialog
import android.os.Bundle
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import com.snails.base.dialog.BaseDialog
import com.snails.base.image_loader.load
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.R
import com.snails.module.base.databinding.ModuleBaseLookLargeImageBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年03月12日 12:47:59
 */
class LookLargeImageDialog() : BaseDialog<ModuleBaseLookLargeImageBinding>() {

    private var showUrl: String? = null

    constructor(showUrl: String?) : this() {
        this.showUrl = showUrl
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return super.onCreateDialog(savedInstanceState).also {
            it.window?.let { window ->
                WindowCompat.setDecorFitsSystemWindows(window, false)
                val windowInsetsController =
                    WindowCompat.getInsetsController(window, window.decorView)
                windowInsetsController.hide(WindowInsetsCompat.Type.statusBars())
            }
        }
    }

    override fun initView() {
        super.initView()
        binding.ivLookLargeImage.load(showUrl)
    }

    override fun initClick() {
        super.initClick()
        binding.ivLookLargeImage.singleClick {
            dismiss()
        }
    }


    override fun getTheme() = R.style.MyFullScreenDialog
}