package com.snails.module.base.dialog

import com.snails.base.dialog.BaseDialog
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.databinding.ModuleBaseParentVerificationDiaogLayoutBinding
import kotlin.random.Random

/**
 * @Description 家长验证弹窗
 * <AUTHOR>
 * @CreateTime 2024年12月25日 10:22:32
 */
class ParentVerificationDialog() : BaseDialog<ModuleBaseParentVerificationDiaogLayoutBinding>() {

    private var next: (() -> Unit)? = null

    constructor(next: () -> Unit) : this() {
        this.next = next
    }

    override fun initView() {
        super.initView()
        binding.parentVerificationView.apply {
            setClockwiseDirection(Random.nextBoolean())
            setOnCompleteListener {
                dismiss()
                next?.invoke()
            }
        }
    }

    override fun initClick() {
        super.initClick()
        binding.ivClose.singleClick {
            dismiss()
        }
    }
}