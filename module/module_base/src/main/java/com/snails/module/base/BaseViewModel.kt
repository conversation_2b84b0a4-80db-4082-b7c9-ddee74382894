package com.snails.module.base

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.ThreadUtils
import com.blankj.utilcode.util.ToastUtils
import com.snails.base.network.bean.BaseResponse
import com.snails.base.network.error.ApiException
import com.snails.base.network.error.ExceptionHandler
import com.snails.base.network.repository.downloadx.Progress
import com.snails.base.network.repository.downloadx.core.BatchDownloadListener
import com.snails.base.network.repository.downloadx.core.DownloadConfig
import com.snails.base.network.repository.downloadx.core.DownloadTask
import com.snails.base.network.repository.downloadx.downloadBatch
import com.snails.base.utils.ext.extractMediaFilesFromObject
import com.snails.module.base.bean.StateType
import com.snails.module.base.utils.SingleLiveEventLiveData
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.launch

/**
 * @Description ViewModel 基类
 * <AUTHOR>
 * @CreateTime 2024年08月10日 10:26:51
 */
open class BaseViewModel : ViewModel() {
    val loadingLiveData: MutableLiveData<Boolean> = SingleLiveEventLiveData()
    val loadingDialogLiveData: MutableLiveData<Boolean> = SingleLiveEventLiveData()
    val errorLiveData: MutableLiveData<Boolean> = SingleLiveEventLiveData()
    val contentLiveData: MutableLiveData<Boolean> = SingleLiveEventLiveData()
    val loginInvalidLiveData: MutableLiveData<Boolean> = SingleLiveEventLiveData() //登录失效


    fun <T> localRequest(
        request: suspend () -> T, //发起请求
        success: ((T?) -> Unit)? = null, //成功回调
        failed: ((String?) -> Unit)? = null, //失败回调
        showLoadingDialog: Boolean = false, //是否显示loadingDialog
    ) {
        viewModelScope.launch {
            // 使用 Flow 代替 launch
            flow {
                // 发射请求的结果
                emit(request())
            }.flowOn(Dispatchers.IO)
                .onStart {
                    if (showLoadingDialog) {
                        loadingDialogLiveData.value = true
                    }
                }
                .catch { e ->
                    failed?.invoke("Local Request Failed")
                    if (showLoadingDialog) {
                        loadingDialogLiveData.value = false
                    }
                }.onCompletion {
                    if (showLoadingDialog) {
                        loadingDialogLiveData.value = false
                    }
                }
                .collect { response ->
                    success?.invoke(response)
                }
        }
    }

    fun <T> request(
        request: suspend () -> BaseResponse<T>, //发起请求
        stateType: StateType? = StateType.NONE, //页面弹窗级别的loading
        showLoading: ((Boolean) -> Unit)? = null, //UI级别的Loading
        hideLoading: (() -> Unit)? = null, //隐藏UI级别的Loading
        success: ((T?) -> Unit)? = null, //成功回调
        failed: ((String?) -> Unit)? = null, //失败回调
        disappearLoadingNow: Boolean = true //loading是否立刻消失
    ) {
        viewModelScope.launch {
            // 使用 Flow 代替 launch
            flow {
                // 发射请求的结果
                emit(request())
            }.flowOn(Dispatchers.IO)
                .onStart {
                    showLoading?.invoke(true) // 如果有 showLoading 就调用
                    if (stateType == StateType.PAGE) {
                        loadingLiveData.value = true
                    } else if (stateType == StateType.DIALOG) {
                        loadingDialogLiveData.value = true
                    }
                }.map {
                    if (it.isFailed()) {
                        if (it.toast?.isNotEmpty() == true) {
                            ToastUtils.showShort(it.toast)
                        }
                        throw ApiException(it.code, it.message)
                    }
                    it
                }.catch { e ->
                    val exception = ExceptionHandler.handleException(e) {
                        loginInvalidLiveData.value = true
                    }
                    if (stateType == StateType.PAGE) {
                        errorLiveData.value = true
                    } else if (stateType == StateType.DIALOG) {
                        loadingDialogLiveData.value = false
                    }
                    // 捕获异常并处理
                    failed?.invoke(exception.errMsg ?: "Request Failed")
                }
                .onCompletion {
                    hideLoading?.invoke() // 如果有 hideLoading 就调用
                    if (disappearLoadingNow) {
                        loadingDialogLiveData.value = false //隐藏页面弹窗级别的loading
                    }
                }
                .collect { response ->
                    success?.invoke(response.data)
                    if (disappearLoadingNow) {
                        contentLiveData.value = true
                    }
                }
        }
    }

    fun <T> requestAndDownloadResources(
        request: suspend () -> BaseResponse<T>, // 数据请求
        stateType: StateType? = StateType.NONE, // 页面弹窗级别的 loading
        showLoading: ((Boolean) -> Unit)? = null, // UI 级别的 loading
        hideLoading: (() -> Unit)? = null, // 隐藏 UI 级别的 loading
        success: ((T?) -> Unit)? = null, // 成功回调
        failed: ((String?) -> Unit)? = null // 失败回调
    ) {
        viewModelScope.launch {
            // Step 1: 进行数据请求
            request(
                request = request,
                stateType = stateType,
                showLoading = showLoading, // 显示 UI Loading
                hideLoading = null, // 不立即隐藏，确保下载时仍显示
                disappearLoadingNow = false, // 页面级 Loading 不立即消失
                success = { data ->
                    try {
                        // Step 2: 数据请求成功后，解析出资源 URL 列表
                        val imageUrls = data?.extractMediaFilesFromObject()
                        // Step 3: 如果有资源需要下载，则启动下载流程
                        if (imageUrls?.isNotEmpty() == true) {
                            downloadResources(
                                imageUrls = imageUrls,
                                showLoading = showLoading, // 传递 showLoading
                                hideLoading = hideLoading, // 传递 hideLoading
                                onDownloadComplete = {
                                    loadingDialogLiveData.postValue(false) // 隐藏页面级 Loading
                                    success?.invoke(data) // 下载完成，回调成功
                                },
                            )
                        } else {
                            // 无资源需要下载，直接完成
                            hideLoading?.invoke() // 隐藏 UI Loading
                            loadingDialogLiveData.postValue(false) // 隐藏页面级 Loading
                            success?.invoke(data)
                        }
                    } catch (t: Throwable) {
                        t.printStackTrace()
                        // 出现异常时，使用网络资源并结束流程
                        hideLoading?.invoke() // 隐藏 UI Loading
                        loadingDialogLiveData.postValue(false) // 隐藏页面级 Loading
                        success?.invoke(data) // 仍回调成功
                    }
                },
                failed = { errorMsg ->
                    hideLoading?.invoke() // 确保失败时隐藏 UI Loading
                    failed?.invoke(errorMsg) // 回调失败
                }
            )
        }
    }

    private fun downloadResources(
        imageUrls: Set<String>,
        showLoading: ((Boolean) -> Unit)? = null, // 新增：控制 UI Loading 显示
        hideLoading: (() -> Unit)? = null, // 新增：控制 UI Loading 隐藏
        onDownloadComplete: (() -> Unit)? = null, // 下载完成回调
    ) {
        showLoading?.invoke(true) // 开始下载前确保 UI Loading 显示
        viewModelScope.downloadBatch(
            imageUrls,
            downloadConfig = DownloadConfig(),
            listener = object :
                BatchDownloadListener {
                override fun onProgress(
                    overallPercent: Int,
                    individualProgress: List<Pair<DownloadTask, Progress>>
                ) {
                }

                override fun onComplete(allTasks: List<DownloadTask>) {
                    ThreadUtils.runOnUiThread {
                        // 全部下载完成
                        hideLoading?.invoke() // 隐藏 UI Loading
                        onDownloadComplete?.invoke()
                        contentLiveData.value = true
                    }
                }

                override fun onError(failedTasks: List<DownloadTask>) {
            }
            })
    }


    fun <A, B> multipleRequests(
        requestExecutor: suspend () -> Pair<A?, B?>, // 外部传递的请求执行逻辑（并行或串行）
        stateType: StateType? = StateType.NONE, // loading 类型
        showLoading: ((Boolean) -> Unit)? = null, // UI级别的Loading
        hideLoading: (() -> Unit)? = null, // 隐藏UI级别的Loading
        success: ((Pair<A?, B?>) -> Unit)? = null, // 成功回调
        failed: ((String?) -> Unit)? = null // 失败回调
    ) {
        viewModelScope.launch {
            // 使用 Flow 代替 launch
            flow {
                emit(requestExecutor()) // 调用外部传入的请求执行逻辑
            }.flowOn(Dispatchers.IO)
                .onStart {
                    // 显示Loading
                    showLoading?.invoke(true)
                    when (stateType) {
                        StateType.PAGE -> loadingLiveData.value = true
                        StateType.DIALOG -> loadingDialogLiveData.value = true
                        else -> {}
                    }
                }
                .catch { e ->
                    // 捕获异常并处理
                    val exception = ExceptionHandler.handleException(e) {
                        loginInvalidLiveData.postValue(true)
                    }
                    // 捕获异常并处理
                    failed?.invoke(exception.errMsg ?: "Request Failed")
                    if (stateType == StateType.PAGE) {
                        errorLiveData.value = true
                    } else if (stateType == StateType.DIALOG) {
                        loadingDialogLiveData.value = false
                    }
                }
                .onCompletion {
                    // 隐藏Loading
                    hideLoading?.invoke()
                    loadingDialogLiveData.value = false
                }
                .collect { result ->
                    // 成功时回调
                    success?.invoke(result)
                    contentLiveData.value = true
                }
        }
    }

    /**
     * 并行逻辑的例子：
     * val parallelExecutor: suspend () -> Triple<String?, Int?, Boolean?> = {
     *     val flowA = flow { emit(requestA()) }.flowOn(Dispatchers.IO)
     *     val flowB = flow { emit(requestB(null)) }.flowOn(Dispatchers.IO)
     *     val flowC = flow { emit(requestC(null)) }.flowOn(Dispatchers.IO)
     *
     *     combine(flowA, flowB, flowC) { resA, resB, resC ->
     *         Triple(
     *             resA.data.takeIf { !resA.isFailed() },
     *             resB.data.takeIf { !resB.isFailed() },
     *             resC.data.takeIf { !resC.isFailed() }
     *         )
     *     }.single() // 等待所有并行请求完成并收集结果
     * }
     *
     * 串行逻辑的例子：
     * val serialExecutor: suspend () -> Triple<String?, Int?, Boolean?> = {
     *     val resA = requestA()
     *     if (resA.isFailed()) throw ApiException(resA.code, resA.message)
     *
     *     val resB = requestB(resA.data!!)
     *     if (resB.isFailed()) throw ApiException(resB.code, resB.message)
     *
     *     val resC = requestC(resB.data!!)
     *     if (resC.isFailed()) throw ApiException(resC.code, resC.message)
     *
     *     Triple(resA.data, resB.data, resC.data)
     * }
     */
    fun <T, R, S> executeRequestWithFlow(
        requestExecutor: suspend () -> Triple<T?, R?, S?>, // 外部传递的请求执行逻辑（并行或串行）
        stateType: StateType? = StateType.NONE, // loading 类型
        showLoading: ((Boolean) -> Unit)? = null, // UI级别的Loading
        hideLoading: (() -> Unit)? = null, // 隐藏UI级别的Loading
        success: ((Triple<T?, R?, S?>) -> Unit)? = null, // 成功回调
        failed: ((String?) -> Unit)? = null // 失败回调
    ) {
        viewModelScope.launch {
            // 使用 Flow 代替 launch
            flow {
                emit(requestExecutor()) // 调用外部传入的请求执行逻辑
            }.flowOn(Dispatchers.IO)
                .onStart {
                    // 显示Loading
                    showLoading?.invoke(true)
                    if (stateType == StateType.PAGE) {
                        loadingLiveData.value = true
                    } else if (stateType == StateType.DIALOG) {
                        loadingDialogLiveData.value = true
                    }
                }
                .catch { e ->
                    // 捕获异常并处理
                    val exception = ExceptionHandler.handleException(e) {
                        loginInvalidLiveData.value = true
                    }
                    // 捕获异常并处理
                    failed?.invoke(exception.errMsg ?: "Request Failed")
                    if (stateType == StateType.PAGE) {
                        errorLiveData.value = true
                    } else if (stateType == StateType.DIALOG) {
                        loadingDialogLiveData.value = false
                    }
                }
                .onCompletion {
                    // 隐藏Loading
                    hideLoading?.invoke()
                    loadingDialogLiveData.value = false
                }
                .collect { result ->
                    // 成功时回调
                    success?.invoke(result)
                    contentLiveData.value = true
                }
        }
    }


    /**
     * 支持 N 个请求的串行和并行处理
     * 并行逻辑的例子：
     * parallel=true
     * val requests = listOf<suspend (Any?) -> BaseResponse<*>>(
     *     { fetchDataA() },
     *     { fetchDataB() },
     *     { fetchDataC() }
     * )
     *
     * 串行逻辑的例子：
     * val requests = listOf<suspend (Any?) -> BaseResponse<*>>(
     *     { fetchDataA() },
     *     { dataA -> fetchDataB(dataA as String) }, // 使用上一个请求的结果
     *     { dataB -> fetchDataC(dataB as Int) }    // 使用上一个请求的结果
     * )
     *
     */
    fun <T> multipleRequests(
        requests: List<suspend (T?) -> BaseResponse<T>?>, // N 个请求，接收上一个请求结果作为参数
        stateType: StateType? = StateType.NONE, // 页面弹窗级别的loading
        showLoading: ((Boolean) -> Unit)? = null, // UI级别的Loading
        hideLoading: (() -> Unit)? = null, // 隐藏UI级别的Loading
        success: ((List<T?>) -> Unit)? = null, // 成功回调，包含所有请求的结果
        failed: ((String?) -> Unit)? = null, // 失败回调
        parallel: Boolean = false // 标志是串行请求还是并行请求
    ) {
        viewModelScope.launch {
            flow {
                if (parallel) {
                    // 并行请求，使用 coroutineScope + async + awaitAll
                    val results = coroutineScope {
                        requests.map { request ->
                            async {
                                try {
                                    request(null).takeIf { it?.isFailed() == true }?.data
                                } catch (e: Exception) {
                                    null // 请求失败返回 null，其他并行请求继续
                                }
                            }
                        }.awaitAll()
                    }
                    emit(results)
                } else {
                    // 串行请求，依次执行
                    val results = mutableListOf<T?>()
                    var previousResult: T? = null
                    for (request in requests) {
                        val response = request(previousResult)
                        if (response?.isFailed() == true) throw ApiException(
                            response.code,
                            response.message
                        )
                        results.add(response?.data)
                        previousResult = response?.data
                    }
                    emit(results)
                }
            }.flowOn(Dispatchers.IO)
                .onStart {
                    showLoading?.invoke(true) // 显示UI加载状态
                    when (stateType) {
                        StateType.PAGE -> loadingLiveData.value = true
                        StateType.DIALOG -> loadingDialogLiveData.value = true
                        else -> {}
                    }
                }
                .catch { e ->
                    errorLiveData.value = true
                    // 捕获异常并处理
                    val exception = ExceptionHandler.handleException(e) {
                        loginInvalidLiveData.postValue(true)
                    }
                    // 捕获异常并处理
                    failed?.invoke(exception.errMsg ?: "Request Failed")
                    if (stateType == StateType.PAGE) {
                        errorLiveData.value = true
                    } else if (stateType == StateType.DIALOG) {
                        loadingDialogLiveData.value = false
                    }
                }
                .onCompletion {
                    hideLoading?.invoke() // 隐藏UI加载状态
                    loadingDialogLiveData.value = false
                }
                .collect { results ->
                    // 成功时调用 success 回调，返回所有请求的结果
                    success?.invoke(results)
                    contentLiveData.value = true
                }
        }
    }
}