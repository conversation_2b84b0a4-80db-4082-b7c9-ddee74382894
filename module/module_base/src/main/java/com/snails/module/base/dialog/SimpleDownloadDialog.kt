package com.snails.module.base.dialog

import androidx.lifecycle.LifecycleCoroutineScope
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.ToastUtils
import com.snails.base.dialog.BaseDialog
import com.snails.base.network.repository.downloadx.State
import com.snails.base.network.repository.downloadx.download
import com.snails.base.utils.constants.AppConstants
import com.snails.base.utils.ext.singleClick
import com.snails.module.base.databinding.ModuleBaseSimpleDownloadDialogLayoutBinding
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年03月24日 12:00:17
 */
class SimpleDownloadDialog() : BaseDialog<ModuleBaseSimpleDownloadDialogLayoutBinding>() {

    private var downloadUrl: String? = null
    private var next: ((String) -> Unit)? = null
    private var lifecycleScope: LifecycleCoroutineScope? = null

    constructor(
        downloadUrl: String,
        lifecycleScope: LifecycleCoroutineScope,
        next: (String) -> Unit
    ) : this() {
        this.downloadUrl = downloadUrl
        this.lifecycleScope = lifecycleScope
        this.next = next
    }

    override fun initView() {
        super.initView()
        downloadUrl?.let { url ->
            lifecycleScope?.let { scope ->
                val filePath = AppConstants.getDownloadPath()
                val fileName = FileUtils.getFileName(url)
                // 创建下载任务
                scope.download(url, saveName = fileName, savePath = filePath).apply {
                    state().onEach { state ->
                        when (state) {
                            is State.Succeed -> {
                                next?.invoke("$filePath/$fileName")
                                dismiss()
                            }

                            is State.Downloading -> {

                            }

                            is State.Failed -> {
                                ToastUtils.showShort("下载失败，请检查网络后重试")
                                dismiss()
                            }

                            is State.None -> {

                            }

                            is State.Stopped -> {

                            }

                            is State.Waiting -> {

                            }
                        }
                        runCatching {
                            binding.numberProgressBar.progress =
                                state.progress.percent().toInt()
                        }
                    }.launchIn(scope)
                }
            }
        }

    }

    override fun initClick() {
        super.initClick()
        binding.ivClose.singleClick {
            dismiss()
        }
    }
}