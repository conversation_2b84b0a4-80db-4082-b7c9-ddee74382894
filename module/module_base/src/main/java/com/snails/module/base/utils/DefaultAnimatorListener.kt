package com.snails.module.base.utils

import android.animation.Animator

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月30日 14:19:12
 */
abstract class DefaultAnimatorListener : Animator.AnimatorListener {
    override fun onAnimationStart(animation: Animator) {
    }

    override fun onAnimationEnd(animation: Animator) {
    }

    override fun onAnimationCancel(animation: Animator) {
    }

    override fun onAnimationRepeat(animation: Animator) {
    }
}