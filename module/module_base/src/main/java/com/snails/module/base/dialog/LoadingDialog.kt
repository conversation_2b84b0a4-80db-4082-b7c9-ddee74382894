package com.snails.module.base.dialog

import android.content.DialogInterface
import android.os.Bundle
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import com.snails.base.dialog.BaseDialog
import com.snails.module.base.databinding.ModuleBaseLoadingDiaogLayoutBinding


/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年09月19日 10:00:17
 */
class LoadingDialog : BaseDialog<ModuleBaseLoadingDiaogLayoutBinding>() {

    companion object {
        private var instance: LoadingDialog? = null

        fun showLoading(manager: FragmentManager) {
            if (instance == null || instance?.isAdded != true) {
                instance = LoadingDialog()
                instance?.show(manager, null)
            }
        }

        fun dismissLoading() {
            instance?.dismissAllowingStateLoss()
            instance = null
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        dialog?.apply {
            setCanceledOnTouchOutside(false)//点击屏幕不消失
            setOnKeyListener(DialogInterface.OnKeyListener { _, keyCode, _ ->
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    return@OnKeyListener true
                }
                false
            })
        }
        return super.onCreateView(inflater, container, savedInstanceState)
    }
}