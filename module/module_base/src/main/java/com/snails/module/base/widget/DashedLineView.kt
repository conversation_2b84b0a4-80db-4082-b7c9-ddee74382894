package com.snails.module.base.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.snails.module.base.R

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月27日 16:43:29
 */
class DashedLineView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var dashLength = 10f   // 默认虚线段长度
    private var dashGap = 5f       // 默认虚线段之间的间隔
    private var dashColor = Color.BLACK // 默认虚线颜色
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)

    init {
        // 从 XML 中获取自定义属性
        context.theme.obtainStyledAttributes(
            attrs,
            R.styleable.DashedLineView,
            0, 0
        ).apply {
            try {
                dashLength = getDimension(R.styleable.DashedLineView_dashLength, dashLength)
                dashGap = getDimension(R.styleable.DashedLineView_dashGap, dashGap)
                dashColor = getColor(R.styleable.DashedLineView_dashColor, dashColor)
            } finally {
                recycle()
            }
        }

        // 设置画笔
        paint.color = dashColor
        paint.style = Paint.Style.STROKE
        paint.strokeWidth = 5f  // 设置虚线宽度
        paint.pathEffect = DashPathEffect(floatArrayOf(dashLength, dashGap), 0f)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 绘制一条竖直虚线
        val startX = width / 2f
        val startY = 0f
        val stopY = height.toFloat()

        canvas.drawLine(startX, startY, startX, stopY, paint)
    }

    // 提供方法让代码动态设置属性
    fun setDashLength(length: Float) {
        dashLength = length
        updatePathEffect()
    }

    fun setDashGap(gap: Float) {
        dashGap = gap
        updatePathEffect()
    }

    fun setDashColor(color: Int) {
        dashColor = color
        paint.color = color
        invalidate()
    }

    private fun updatePathEffect() {
        paint.pathEffect = DashPathEffect(floatArrayOf(dashLength, dashGap), 0f)
        invalidate()
    }
}