package com.snails.module.base.utils;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.navigation.NavDestination;
import androidx.navigation.NavOptions;
import androidx.navigation.Navigator;
import androidx.navigation.fragment.FragmentNavigator;

import java.util.ArrayDeque;
import java.util.Map;

@Navigator.Name("keep_state_fragment")
public class KeepStateNavigator extends Navigator<FragmentNavigator.Destination> {
    private static final String TAG = "FragmentNavigator";

    private final Context mContext;
    private final FragmentManager mFragmentManager;
    private final int mContainerId;
    private ArrayDeque<String> mBackStack = new ArrayDeque<>();

    public KeepStateNavigator(@NonNull Context context, @NonNull FragmentManager manager,
                              int containerId) {
        mContext = context;
        mFragmentManager = manager;
        mContainerId = containerId;
    }

    @NonNull
    @Override
    public FragmentNavigator.Destination createDestination() {
        return new FragmentNavigator.Destination(this);
    }

    @Nullable
    @Override
    public NavDestination navigate(
            @NonNull FragmentNavigator.Destination destination,
            @Nullable Bundle args,
            @Nullable NavOptions navOptions,
            @Nullable Extras navigatorExtras) {

        if (mFragmentManager.isStateSaved()) {
            Log.i(TAG, "Ignoring navigate() call: FragmentManager has already saved its state");
            return null;
        }

        String tag = destination.getId() + "";

        String className = destination.getClassName();
        if (className.charAt(0) == '.') {
            className = mContext.getPackageName() + className;
        }

        final FragmentTransaction ft = mFragmentManager.beginTransaction();

        int enterAnim = navOptions != null ? navOptions.getEnterAnim() : -1;
        int exitAnim = navOptions != null ? navOptions.getExitAnim() : -1;
        int popEnterAnim = navOptions != null ? navOptions.getPopEnterAnim() : -1;
        int popExitAnim = navOptions != null ? navOptions.getPopExitAnim() : -1;
        if (enterAnim != -1 || exitAnim != -1 || popEnterAnim != -1 || popExitAnim != -1) {
            enterAnim = enterAnim != -1 ? enterAnim : 0;
            exitAnim = exitAnim != -1 ? exitAnim : 0;
            popEnterAnim = popEnterAnim != -1 ? popEnterAnim : 0;
            popExitAnim = popExitAnim != -1 ? popExitAnim : 0;
            ft.setCustomAnimations(enterAnim, exitAnim, popEnterAnim, popExitAnim);
        }

//        final Fragment frag = instantiateFragment(mContext, mFragmentManager, className, args);
//        frag.setArguments(args);
//        ft.replace(mContainerId, frag);
//        ft.setPrimaryNavigationFragment(frag);

        Fragment currentFragment = mFragmentManager.getPrimaryNavigationFragment();
        if (currentFragment != null) {
            ft.hide(currentFragment);
        }
        Fragment fragment = mFragmentManager.findFragmentByTag(tag);
        if (fragment == null) {
            fragment = instantiateFragment(mContext, mFragmentManager, className);
            fragment.setArguments(args);
            ft.add(mContainerId, fragment, tag);
        } else {
            fragment.setArguments(args);
            ft.show(fragment);
        }
        ft.setPrimaryNavigationFragment(fragment);

        final boolean initialNavigation = mBackStack.isEmpty();
        final boolean isSingleTopReplacement = navOptions != null
                && !initialNavigation
                && navOptions.shouldLaunchSingleTop()
                && mBackStack.peekLast().equals(tag);

        boolean isAdded;
        if (initialNavigation) {
            isAdded = true;
        } else if (isSingleTopReplacement) {
            // TODO 暂不支持 singleTop
            // Single Top means we only want one instance on the back stack
//            if (mBackStack.size() > 1) {
            // If the Fragment to be replaced is on the FragmentManager's
            // back stack, a simple replace() isn't enough so we
            // remove it from the back stack and put our replacement
            // on the back stack in its place

//                mFragmentManager.popBackStack(generateBackStackName(mBackStack.size(), mBackStack.peekLast()),
//                        FragmentManager.POP_BACK_STACK_INCLUSIVE);
//                ft.addToBackStack(generateBackStackName(mBackStack.size(), destId));
//            }
            isAdded = false;
        } else {
//            ft.addToBackStack(generateBackStackName(mBackStack.size() + 1, destId));
            isAdded = true;
        }
        if (navigatorExtras instanceof FragmentNavigator.Extras) {
            FragmentNavigator.Extras extras = (FragmentNavigator.Extras) navigatorExtras;
            for (Map.Entry<View, String> sharedElement : extras.getSharedElements().entrySet()) {
                ft.addSharedElement(sharedElement.getKey(), sharedElement.getValue());
            }
        }
        ft.setReorderingAllowed(true);
        ft.commitNow();
        // The commit succeeded, update our view of the world
        if (isAdded) {
            mBackStack.add(tag);
            return destination;
        } else {
            return null;
        }
    }

    @Override
    public boolean popBackStack() {
        if (mBackStack.isEmpty()) {
            return false;
        }
        if (mFragmentManager.isStateSaved()) {
            Log.i(TAG, "Ignoring popBackStack() call: FragmentManager has already saved its state");
            return false;
        }

//        mFragmentManager.popBackStack();
//        mFragmentManager.popBackStack(
//                generateBackStackName(mBackStack.size(), mBackStack.peekLast()),
//                FragmentManager.POP_BACK_STACK_INCLUSIVE);
        String tag = mBackStack.removeLast();
        return doNavigate(tag);
    }

    private boolean doNavigate(String removeTag) {
        FragmentTransaction transaction = mFragmentManager.beginTransaction();
        Fragment removeFrag = mFragmentManager.findFragmentByTag(removeTag);
        if (removeFrag != null) {
            transaction.hide(removeFrag);
        } else {
            return false;
        }

        // TODO 解决会闪现 navigation start dest 页面
        if (mBackStack.size() == 1) {
            transaction.commitNow();
            return true;
        }

        String showTag;
        if (mBackStack.size() > 0)
            showTag = mBackStack.getLast();
        else return false;

        Fragment showFrag = mFragmentManager.findFragmentByTag(showTag);
        if (showFrag != null) {
            transaction.show(showFrag);
            transaction.setPrimaryNavigationFragment(showFrag);
            transaction.setReorderingAllowed(true);
            boolean stateSaved = mFragmentManager.isStateSaved();
            if (stateSaved) {
                transaction.commitNowAllowingStateLoss();
            } else {
                transaction.commitNow();
            }
        } else {
            return false;
        }
        return true;
    }

    private String generateBackStackName(int backStackIndex, int destId) {
        return backStackIndex + "-" + destId;
    }

    public Fragment instantiateFragment(@NonNull Context context,
                                        @NonNull FragmentManager fragmentManager,
                                        @NonNull String className) {
        return fragmentManager.getFragmentFactory().instantiate(context.getClassLoader(), className);
    }
}
