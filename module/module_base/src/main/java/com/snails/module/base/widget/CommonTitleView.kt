package com.snails.module.base.widget

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.ColorUtils
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.R
import androidx.core.content.withStyledAttributes

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月16日 12:08:40
 */
class CommonTitleView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val ivTitleBack: AppCompatImageView
    private val tvTitle: AppCompatTextView
    private val tvSubTitle: AppCompatTextView
    private val tvRightTxt: AppCompatTextView
    private val clyContainer: ConstraintLayout
    private val ivRightIcon: AppCompatImageView

    init {
        // 使用 LayoutInflater 加载布局
        val view = LayoutInflater.from(context)
            .inflate(R.layout.module_base_common_title_view_layout, this, true)

        ivTitleBack = view.findViewById(R.id.ivTitleBack)
        tvTitle = view.findViewById(R.id.tvTitle)
        tvSubTitle = view.findViewById(R.id.tvSubTitle)
        tvRightTxt = view.findViewById(R.id.tvRightTxt)
        clyContainer = view.findViewById(R.id.clyContainer)
        ivRightIcon = view.findViewById(R.id.ivRightIcon)

        // 读取自定义属性（如果有）
        attrs?.let {
            context.withStyledAttributes(it, R.styleable.CommonTitleView, 0, 0) {
                val title = getString(R.styleable.CommonTitleView_commTitle) ?: ""
                val subTitle = getString(R.styleable.CommonTitleView_commSubTitle) ?: ""
                val rightTxt = getString(R.styleable.CommonTitleView_rightTxt) ?: ""
                val color = getColor(R.styleable.CommonTitleView_commTitleBg, Color.TRANSPARENT)
                val titleColor = getColor(
                    R.styleable.CommonTitleView_commTitleColor,
                    ColorUtils.getColor(R.color.text_headline)
                )
                val backIcon =
                    getResourceId(
                        R.styleable.CommonTitleView_commBackIcon,
                        R.drawable.svg_common_back
                    )
                val rightIcon =
                    getResourceId(
                        R.styleable.CommonTitleView_rightIcon,
                        R.drawable.svg_share_bw
                    )
                val showBackButton = getBoolean(R.styleable.CommonTitleView_commShowBackBtn, true)
                val showRightIcon = getBoolean(R.styleable.CommonTitleView_showRightIcon, false)
                clyContainer.setBackgroundColor(color)
                tvTitle.setTextColor(titleColor)
                setTitle(title)
                setSubTitle(subTitle)
                setRight(rightTxt)
                setBackIcon(backIcon)
                setRightIcon(rightIcon)
                showBackButton(showBackButton)
                showRightIcon(showRightIcon)
            }
        }
    }

    fun getTitleView() = tvTitle

    // 设置标题文字
    fun setTitle(title: String) {
        tvTitle.text = title
    }

    // 设置子标题文字
    fun setSubTitle(title: String) {
        if (title.isEmpty()) {
            return
        }
        tvSubTitle.visible()
        tvSubTitle.text = title
    }

    fun setRight(rightTxt: String) {
        tvRightTxt.text = rightTxt
        setRightVisible(true)
    }

    fun setRightVisible(show: Boolean) {
        if (show) {
            tvRightTxt.visible()
        } else {
            tvRightTxt.gone()
        }
    }

    // 设置右边的文字
    fun setBackIcon(id: Int) {
        ivTitleBack.setImageResource(id)
    }

    // 设置右边Icon
    fun setRightIcon(id: Int) {
        ivRightIcon.setImageResource(id)
    }

    // 设置返回按钮的可见性
    fun showBackButton(show: Boolean) {
        ivTitleBack.visibility = if (show) View.VISIBLE else View.GONE
    }

    // 设置右边按钮的可见性
    fun showRightIcon(show: Boolean) {
        ivRightIcon.visibility = if (show) View.VISIBLE else View.GONE
    }

    // 设置返回按钮点击事件
    fun setBackClickListener(listener: OnClickListener) {
        ivTitleBack.singleClick(listener)
    }

    fun setRightTxtClickListener(listener: OnClickListener) {
        tvRightTxt.singleClick(listener)
    }

    // 设置右边按钮点击事件
    fun setRightIconClickListener(listener: OnClickListener) {
        ivRightIcon.singleClick(listener)
    }
}