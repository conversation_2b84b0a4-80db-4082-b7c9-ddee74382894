package com.snails.module.base.dialog

import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.utils.ext.singleClick
import com.snails.common.widget.WheelView
import com.snails.module.base.databinding.ModuleBaseDialogDateBinding
import java.time.YearMonth
import java.util.Calendar

/**
 * @Description 日期选择
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:34:47
 */
class DateBottomDialog() : BaseBottomDialog<ModuleBaseDialogDateBinding>(heightDimen = 332) {

    //当前选中的年月日
    private var currentSelectYear: String? = null
    private var currentSelectMonth: String? = null
    private var currentSelectDay: String? = null

    //当前选中的年、月、日
    private var currentSelectYearIndex: Int = 0
    private var currentSelectMonthIndex: Int = 0
    private var currentSelectDayIndex: Int = 0

    private val currentYear = Calendar.getInstance().get(Calendar.YEAR)
    private val currentMonth = Calendar.getInstance().get(Calendar.MONTH) + 1
    private val currentDay = Calendar.getInstance().get(Calendar.DAY_OF_MONTH)

    private var currentYearIndex: Int? = null
    private var currentMonthIndex: Int? = null
    private var currentDayIndex: Int? = null
    private var selected: ((String) -> Unit)? = null
    private var selectedPos: ((Int, Int, Int) -> Unit)? = null

    constructor(
        currentYearIndex: Int?,
        currentMonthIndex: Int?,
        currentDayIndex: Int?,
        selected: (String) -> Unit,
        selectedPos: (Int, Int, Int) -> Unit
    ) : this() {
        this.currentYearIndex = currentYearIndex
        this.currentMonthIndex = currentMonthIndex
        this.currentDayIndex = currentDayIndex
        this.selected = selected
        this.selectedPos = selectedPos
    }


    override fun initData() {
        super.initData()
        binding.wheelViewYear.setDataItems(generateYearList().toMutableList())
        binding.wheelViewMonth.setDataItems(generateMonthList(currentMonth).toMutableList())
        binding.wheelViewDay.setDataItems(generateDayList(currentDay).toMutableList())

        currentYearIndex?.let { currentYearIndex ->
            binding.wheelViewYear.setSelectedItemPosition(currentYearIndex)
            currentSelectYearIndex = currentYearIndex
        }
        currentMonthIndex?.let { currentMonthIndex ->
            binding.wheelViewMonth.setSelectedItemPosition(currentMonthIndex)
            currentSelectMonthIndex = currentMonthIndex
        }
        currentDayIndex?.let { currentDayIndex ->
            binding.wheelViewDay.setSelectedItemPosition(currentDayIndex)
            currentSelectDayIndex = currentDayIndex
        }
    }

    override fun initClick() {
        super.initClick()
        //年
        binding.wheelViewYear.setOnItemSelectedListener(object : WheelView.OnItemSelectedListener {
            override fun onItemSelected(wheelView: WheelView, data: Any, position: Int) {
                currentSelectYearIndex = position
                currentSelectYear = data.toString().split("年")[0]
                //当前年发生变化时，动态设置月份和天数
                if (currentSelectYear == currentYear.toString()) {
                    binding.wheelViewMonth.setDataItems(generateMonthList(currentMonth).toMutableList())
                    if (currentSelectMonth == currentMonth.toString().padStart(2, '0')) {
                        binding.wheelViewDay.setDataItems(generateDayList(currentDay).toMutableList())
                    } else {
                        binding.wheelViewDay.setDataItems(generateDayList().toMutableList())
                    }
                } else {
                    binding.wheelViewMonth.setDataItems(generateMonthList(12).toMutableList())
                    binding.wheelViewDay.setDataItems(generateDayList().toMutableList())
                }
            }
        })
        //月
        binding.wheelViewMonth.setOnItemSelectedListener(object : WheelView.OnItemSelectedListener {
            override fun onItemSelected(wheelView: WheelView, data: Any, position: Int) {
                currentSelectMonthIndex = position
                currentSelectMonth = data.toString().split("月")[0]
                //当前月发生变化时，动态设置天数
                if (currentSelectMonth == currentMonth.toString().padStart(2, '0')) {
                    binding.wheelViewDay.setDataItems(generateDayList(currentDay).toMutableList())
                } else {
                    binding.wheelViewDay.setDataItems(generateDayList().toMutableList())
                }
            }
        })
        //日
        binding.wheelViewDay.setOnItemSelectedListener(object : WheelView.OnItemSelectedListener {
            override fun onItemSelected(wheelView: WheelView, data: Any, position: Int) {
                currentSelectDayIndex = position
                currentSelectDay = data.toString().split("日")[0]
            }
        })
        binding.tvCancel.singleClick {
            dismiss()
        }
        binding.tvSure.singleClick {
            selected?.invoke("$currentSelectYear-$currentSelectMonth-$currentSelectDay")
            selectedPos?.invoke(
                currentSelectYearIndex,
                currentSelectMonthIndex,
                currentSelectDayIndex
            )
            dismiss()
        }
    }

    private fun generateYearList(): List<String> {
        return (1990..currentYear).reversed().map { "${it}年" }
    }

    private fun generateMonthList(maxMonth: Int): List<String> {
        return (1..maxMonth).reversed().map { month -> "${month.toString().padStart(2, '0')}月" }
    }

    fun generateDayList(maxDay: Int? = null): List<String> {
        if (maxDay != null) {
            return (1..maxDay).reversed().map { day -> "${day.toString().padStart(2, '0')}日" }
        }
        val year = currentSelectYear?.toInt() ?: Calendar.getInstance().get(Calendar.YEAR)
        val month = currentSelectMonth?.toInt() ?: 1
        val daysInMonth = YearMonth.of(year, month).lengthOfMonth()
        return (1..daysInMonth).reversed().map { day -> "${day.toString().padStart(2, '0')}日" }
    }
}