package com.snails.module.base.widget.refresh

import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.airbnb.lottie.LottieAnimationView
import com.scwang.smart.refresh.layout.simple.SimpleComponent
import com.snails.module.base.R

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年12月04日 11:40:17
 */
class CommonRefresh @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : SimpleComponent(context, attrs, defStyleAttr) {

    private var lottieCommRefresh: LottieAnimationView? = null

    init {
        val view = View.inflate(context, R.layout.module_base_common_refresh, this)
        lottieCommRefresh = view.findViewById(R.id.lottieCommRefresh)
    }
}