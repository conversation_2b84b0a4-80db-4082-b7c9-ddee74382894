plugins {
    alias(libs.plugins.common.gradle)
    alias(libs.plugins.kotlin.ksp)
}

android {
    defaultConfig {
        ndk {
            abiFilters.addAll(arrayOf("arm64-v8a"))
        }
    }

    dependencies {
        implementation(project(":module:module_base"))
        compileOnly(files("../../base/base_aar/libs/NativeCamera.aar"))
        compileOnly(files("../../base/base_aar/libs/unityLibrary-release.aar"))
        ksp(libs.router.ksp)

        implementation("androidx.camera:camera-core:1.3.1")
        implementation("androidx.camera:camera-camera2:1.3.1")
        implementation("androidx.camera:camera-lifecycle:1.3.1")
        implementation("androidx.camera:camera-video:1.3.1")
        implementation("androidx.camera:camera-view:1.3.1")
        implementation("androidx.camera:camera-extensions:1.3.1")
        implementation("com.google.guava:guava:31.1-android")
    }
}