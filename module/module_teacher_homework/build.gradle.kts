plugins {
    alias(libs.plugins.common.gradle)
    alias(libs.plugins.kotlin.ksp)
}

dependencies {
    implementation(project(":module:module_base"))
    implementation(project(":common:common_widget"))
    implementation(project(":base:base_record"))
    implementation(project(":base:base_video_player"))
    implementation(project(":base:base_audio"))
    implementation(project(":base:base_audio_player"))
    implementation(project(":base:base_ffmpeg"))

    implementation(libs.permissions)
    implementation(libs.immersionbar)
    ksp(libs.router.ksp)

    implementation(libs.glide)

    // PictureSelector 基础 (必须)
    implementation(libs.pictureselector)
    // 图片裁剪
    implementation(libs.pictureselector.ucrop)
    //刷新
    implementation(libs.refresh.layout.kernel)      //核心必须依赖
}