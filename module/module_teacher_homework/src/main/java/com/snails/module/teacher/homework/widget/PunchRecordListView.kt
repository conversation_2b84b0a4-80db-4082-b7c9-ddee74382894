package com.snails.module.teacher.homework.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.ISame
import com.snails.base.network.repository.info.teacher.PunchRecordOverViewInfo
import com.snails.base.network.repository.info.teacher.SubmitItem
import com.snails.module.teacher.homework.bean.EmptyBean
import com.snails.module.teacher.homework.viewbinder.CheckInDetailsItemViewBinder
import com.snails.module.teacher.homework.viewbinder.CheckInOverviewItemViewBinder
import com.snails.module.teacher.homework.viewbinder.LearningDataEmptyViewBinder

/**
 * @Description 未打卡列表
 * <AUTHOR>
 * @CreateTime 2025年01月09日11:11:18
 */
class PunchRecordListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val listAdapter = MultiTypeAdapter()

    init {
        initView()
    }

    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(PunchRecordOverViewInfo::class.java, CheckInOverviewItemViewBinder())
            register(SubmitItem::class.java, CheckInDetailsItemViewBinder())
            register(EmptyBean::class.java, LearningDataEmptyViewBinder())
        }
        adapter = listAdapter
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(new: List<ISame>) {
        listAdapter.items = new
        listAdapter.notifyDataSetChanged()
    }
}