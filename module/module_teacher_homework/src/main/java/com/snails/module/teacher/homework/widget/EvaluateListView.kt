package com.snails.module.teacher.homework.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.view.View
import androidx.appcompat.widget.AppCompatCheckBox
import androidx.recyclerview.widget.RecyclerView
import com.airbnb.lottie.LottieAnimationView
import com.snails.base.audio_player.AudioPlayManager
import com.snails.base.audio_player.interfaces.PlayStateListener
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.ISame
import com.snails.base.network.repository.info.teacher.HomeworkDetailsItemInfo
import com.snails.base.network.repository.info.teacher.QuoteContentInfo
import com.snails.module.teacher.homework.R
import com.snails.module.teacher.homework.viewbinder.EvaluateItemViewBinder
import com.snails.module.teacher.homework.viewbinder.QuoteItemViewBinder

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月27日 16:26:40
 */
class EvaluateListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val paddingTop = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_12)
    private val paddingBottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_20)
    private val paddingQuoteBottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_8)
    private val aiTalkChatList = mutableListOf<ISame>()
    private val listAdapter = MultiTypeAdapter()
    private var handler: Handler? = null
    private var currentPlayView: LottieAnimationView? = null
    private var evaluateItemViewBinder: EvaluateItemViewBinder? = null

    var listener: ItemClickListener? = null

    private val playStateListener = object : PlayStateListener {
        override fun play() {
            currentPlayView?.playAnimation()

        }

        override fun stop() {
            currentPlayView?.apply {
                pauseAnimation()
                frame = 0
            }
        }

        override fun error(t: Throwable) {
        }
    }

    init {
        handler = Handler(Looper.getMainLooper())
        initView()
    }

    private fun initView() {
        evaluateItemViewBinder = EvaluateItemViewBinder(
            clickAudio = { info, view ->
                currentPlayView = view
                AudioPlayManager.getInstance().play(info.resource)
            },
            longClick = { index, info ->
                listener?.longClick(index, info)
            },
            lookLargeImage = {
                listener?.lookLargeImage(it)
            },
            select = { isChecked, info ->
                listener?.select(isChecked, info)
            }
        )
        evaluateItemViewBinder?.let {
            // 列表项
            listAdapter.apply {
                register(HomeworkDetailsItemInfo::class.java, it)
            }
        }
        // 列表项
        listAdapter.apply {
            //引用评论
            register(QuoteContentInfo::class.java, QuoteItemViewBinder {
                clickQuote(it)
            })
        }
        adapter = listAdapter
        addItemDecoration(ItemDecoration())
    }

    private fun clickQuote(quote: QuoteContentInfo) {
        var position = -1
        run loop@{
            aiTalkChatList.forEachIndexed { index, data ->
                if (data is HomeworkDetailsItemInfo) {
                    if (data.id == quote.id) {
                        position = index
                        return@loop
                    }
                }
            }
        }
        if (position != -1) {
            smoothScrollToPosition(position)
        }
    }

    /**
     * 设置数据
     * @param isAdd 是否是添加数据，首次进入页面和切换主题都为false，AI对话时，为true
     * @param isScrollToBottom 是否需要滚动到列表底部
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setListData(
        tabList: List<ISame>,
        isAdd: Boolean = false,
        addStart: Boolean = true,
        isScrollToBottom: Boolean = true,
    ) {
        val newList = if (isAdd) {
            if (addStart) {
                aiTalkChatList.addAll(0, tabList)
            } else {
                aiTalkChatList.addAll(tabList)
            }
            aiTalkChatList
        } else {
            aiTalkChatList.clear()
            aiTalkChatList.addAll(tabList)
            tabList
        }
        listAdapter.items = newList
        listAdapter.notifyDataSetChanged()
        if (isScrollToBottom) {
            scrollToBottom(300)
        }
    }

    /**
     * 清除哪个状态的数据
     */
    @SuppressLint("NotifyDataSetChanged")
    fun addData(info: HomeworkDetailsItemInfo) {
        aiTalkChatList.add(info)
        listAdapter.items = aiTalkChatList
        listAdapter.notifyDataSetChanged()
        // 滚动到最底部
        scrollToBottom(100)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addListData(data: List<HomeworkDetailsItemInfo>) {
        aiTalkChatList.addAll(data)
        listAdapter.items = aiTalkChatList
        listAdapter.notifyDataSetChanged()
        // 滚动到最底部
        scrollToBottom(100)
    }

    @SuppressLint("NotifyDataSetChanged")
    fun addListDataSame(data: List<ISame>) {
        aiTalkChatList.addAll(data)
        listAdapter.items = aiTalkChatList
        listAdapter.notifyDataSetChanged()
        // 滚动到最底部
        scrollToBottom(100)
    }

    fun getChatListData() = aiTalkChatList

    @SuppressLint("NotifyDataSetChanged")
    fun removeData(index: Int, info: HomeworkDetailsItemInfo) {
        val next = index + 1
        if (next >= 0 && next <= aiTalkChatList.size - 1) {
            val data = aiTalkChatList[next]
            if (data is QuoteContentInfo && data.id == info.refCourseworkId) {
                aiTalkChatList.remove(data)
            }
        }
        aiTalkChatList.remove(info)
        listAdapter.items = aiTalkChatList
        listAdapter.notifyDataSetChanged()
    }

    /**
     * 滚动到列表底部
     */
    fun scrollToBottom(delayMillis: Long = 0) {
        handler?.postDelayed({
            runCatching {
                smoothScrollToPosition(aiTalkChatList.size)
            }
        }, delayMillis)
    }

    fun addPlayState() {
        AudioPlayManager.getInstance().addPlayStateListener(playStateListener)
    }

    fun removePlayState() {
        AudioPlayManager.getInstance().removePlayStateListener(playStateListener)
    }

    fun clean() {
        handler?.removeCallbacksAndMessages(null)
        handler = null
        listener = null
    }

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State
        ) {
            super.getItemOffsets(outRect, view, parent, state)

            kotlin.runCatching {
                val position = parent.getChildAdapterPosition(view)
                // 判断是否为最后一个数据项
                if (position == 0) {
                    outRect.top = paddingTop
                    outRect.bottom = paddingBottom
                } else {
                    val index = position + 1
                    if (index <= aiTalkChatList.size - 1) {
                        if (aiTalkChatList[index] is QuoteContentInfo) {
                            outRect.bottom = paddingQuoteBottom
                        } else {
                            outRect.bottom = paddingBottom
                        }
                    } else {
                        outRect.bottom = paddingBottom
                    }
                }
            }
        }
    }

    fun getTeacherEvaluateData(): List<HomeworkDetailsItemInfo> {
        val list = mutableListOf<HomeworkDetailsItemInfo>()
        aiTalkChatList.forEach {
            if (it is HomeworkDetailsItemInfo) {
                if (it.messageType == "TEACHER" || it.messageType == "TEACHER_CORRECTING") {
                    list.add(it)
                }
            }
        }
        return list
    }

    interface ItemClickListener {
        fun longClick(index: Int, info: HomeworkDetailsItemInfo)
        fun lookLargeImage(info: String?)
        fun select(isChecked: AppCompatCheckBox, info: HomeworkDetailsItemInfo)
    }
}