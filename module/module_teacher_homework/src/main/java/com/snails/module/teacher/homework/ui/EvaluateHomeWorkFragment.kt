package com.snails.module.teacher.homework.ui

import android.annotation.SuppressLint
import android.view.MotionEvent
import android.view.View
import android.widget.SeekBar
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.widget.AppCompatCheckBox
import androidx.fragment.app.activityViewModels
import androidx.navigation.findNavController
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.ThreadUtils
import com.blankj.utilcode.util.ToastUtils
import com.gyf.immersionbar.ImmersionBar
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import com.snails.base.audio.AudioPlayerManager
import com.snails.base.audio.bean.PlayState
import com.snails.base.audio.bean.RepeatMode
import com.snails.base.audio.interfaces.IPlayProgressListener
import com.snails.base.audio.interfaces.IPlayStateListener
import com.snails.base.audio.interfaces.IPlayTimeListener
import com.snails.base.audio_player.AudioPlayManager
import com.snails.base.ffmpeg.FFmpegUtils
import com.snails.base.ffmpeg.callback.CommandCallback
import com.snails.base.network.repository.info.ISame
import com.snails.base.network.repository.info.teacher.HomeworkDetailsDataInfo
import com.snails.base.network.repository.info.teacher.HomeworkDetailsItemInfo
import com.snails.base.network.repository.info.teacher.toQuoteContentInfo
import com.snails.base.network.repository.storage.TeacherStorage
import com.snails.base.record.RecorderManager
import com.snails.base.record.bean.RecordErrorInfo
import com.snails.base.record.interfaces.RecordingStateListener
import com.snails.base.utils.constants.AppConstants
import com.snails.base.utils.ext.getRealPath
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.toUniqueHash
import com.snails.base.utils.ext.visible
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.bean.StateType
import com.snails.module.base.dialog.LookLargeImageDialog
import com.snails.module.base.dialog.SpeedBottomDialog
import com.snails.module.base.utils.SnailTextWatcher
import com.snails.module.teacher.homework.R
import com.snails.module.teacher.homework.databinding.FragmentEvaluateHomeworkBinding
import com.snails.module.teacher.homework.dialog.ChoosePicBottomDialog
import com.snails.module.teacher.homework.dialog.DeleteTipsDialog
import com.snails.module.teacher.homework.viewmodel.TeacherHomeworkViewModel
import com.snails.module.teacher.homework.widget.EvaluateListView.ItemClickListener
import kotlin.math.ceil

/**
 * @Description 作业点评
 * <AUTHOR>
 * @CreateTime 2025年01月10日 14:34:10
 */
class EvaluateHomeWorkFragment : BaseStateFragment<FragmentEvaluateHomeworkBinding>() {

    private var showRecordLayout = true
    private val teacherHomeworkViewModel: TeacherHomeworkViewModel by activityViewModels()
    private var recordingStateListener: RecordingStateListener? = null
    private var startRecordTime = 0L //开始录音时间
    private var endRecordTime = 0L //结束录音时间
    private var mergeAudioPath: String? = null //合并后的音频地址
    private val teacherInfo = TeacherStorage.me.getTeacherInfo()
    private var playSpeed = TeacherStorage.me.getPlaySpeed()
    private var speedBottomDialog: SpeedBottomDialog? = null
    private var needMergeAudioFile: Boolean = false //是否需要合并音频文件
    private var rawHomeworkDetailsDataList = mutableListOf<HomeworkDetailsItemInfo>()

    private var homeworkDetailsDataInfo: HomeworkDetailsDataInfo? = null

    private val playProgressListener = object : IPlayProgressListener {
        override fun playProgress(progress: Int) {
            binding.seekBar.progress = progress
        }

        override fun bufferProgress(progress: Int) {
        }
    }
    private val playTimeListener = object : IPlayTimeListener {
        override fun currentTime(time: String) {
            binding.tvCurrentTime.text = time
        }

        override fun totalTime(durationTime: Long, time: String) {
            binding.tvTotalTime.text = time
        }
    }
    private val playStateListener = object : IPlayStateListener {
        override fun playing() {
            binding.ivPlayControl.setBackgroundResource(R.drawable.svg_play_pause_bw)
        }

        override fun buffering() {
            binding.ivPlayControl.setBackgroundResource(R.drawable.svg_play_play_bw)
        }

        override fun pause() {
            binding.ivPlayControl.setBackgroundResource(R.drawable.svg_play_play_bw)
        }

        override fun stop() {
            binding.ivPlayControl.setBackgroundResource(R.drawable.svg_play_play_bw)
        }

        override fun error() {
            binding.ivPlayControl.setBackgroundResource(R.drawable.svg_play_play_bw)
        }
    }

    private val selectAiList = mutableListOf<HomeworkDetailsItemInfo>()
    private val selectCheckBoxList = mutableListOf<AppCompatCheckBox>()

    override fun createViewModel() = teacherHomeworkViewModel

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(true).init()
    }

    override fun initData() {
        super.initData()
        teacherHomeworkViewModel.isAiCorrecting = getBooleanExtra("isAiCorrecting")
        teacherHomeworkViewModel.groupingId = getStringExtra("groupingId")
        getStringExtra("studentUid")?.let {
            teacherHomeworkViewModel.studentUid = it
        }
        getBooleanExtra("canEvaluate").let {
            teacherHomeworkViewModel.canEvaluate = it
        }
        getBooleanExtra("fromDubbing").let {
            teacherHomeworkViewModel.fromDubbing = it
        }
        teacherHomeworkViewModel.getHomeworkDetails(StateType.PAGE)

        AudioPlayerManager.init {
            AudioPlayerManager.setRepeatMode(RepeatMode.REPEAT_MODE_OFF)
        }
    }

    override fun initObserve() {
        super.initObserve()
        teacherHomeworkViewModel.homeworkDetailsDataLiveData.observe(this) {
            initUiInfo(it)
            dynamicBottomLayout()
        }
        teacherHomeworkViewModel.autoHomeworkDetailsDataLiveData.observe(this) { info ->
            info.items?.let {
                binding.evaluateListView.addListDataSame(handleQuoteDataFromRawData(it))
            }
            clearAiCorrectingData()
        }
        binding.evaluateListView.addPlayState()
    }

    @SuppressLint("ClickableViewAccessibility", "SetTextI18n")
    override fun initClick() {
        super.initClick()
        binding.apply {
            //返回
            commonTitleView.setBackClickListener {
                back(it)
            }
            //提交
            commonTitleView.setRightTxtClickListener {
                val list = evaluateListView.getTeacherEvaluateData()
                if (list.isEmpty()) {
                    ToastUtils.showShort("请先点评作业")
                    return@setRightTxtClickListener
                }
                teacherHomeworkViewModel.teacherCorrecting(list) {
                    back(commonTitleView)
                }
            }
            //切换文字or语音
            ivKeyboardOrRecord.singleClick {
                if (showRecordLayout) {
                    //显示输入框
                    KeyboardUtils.showSoftInput(clyBottomContainer)
                    flyBottomContainer.setBackgroundResource(R.drawable.shape_sbw_24r_bg)
                    ivKeyboardOrRecord.setBackgroundResource(R.drawable.svg_gray_record)
                    tvRecord.gone()
                    clyInputContent.visible()
                    etInputContent.requestFocus()
                    showRecordLayout = false
                } else {
                    //显示录音
                    KeyboardUtils.hideSoftInput(clyBottomContainer)
                    flyBottomContainer.setBackgroundResource(R.drawable.shape_spb_30r_bg)
                    ivKeyboardOrRecord.setBackgroundResource(R.drawable.svg_keyboard)
                    tvRecord.visible()
                    clyInputContent.gone()
                    showRecordLayout = true
                }
            }
            //触摸录音
            tvRecord.setOnTouchListener { v, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        AudioPlayerManager.pause()
                        if (!checkPermission()) {
                            requestPermission()
                            return@setOnTouchListener false
                        }
                        AudioPlayManager.getInstance().stop()
                        lottieRecord.apply {
                            visible()
                            playAnimation()
                        }
                        clyPlayContainer.gone()
                        evaluateListView.scrollToBottom()
                        v.setBackgroundResource(R.drawable.shape_spbp_30r_bg)
                        //录音
                        RecorderManager.getInstance().startRecord()
                    }

                    MotionEvent.ACTION_CANCEL,
                    MotionEvent.ACTION_UP -> {
                        lottieRecord.apply {
                            gone()
                            pauseAnimation()
                        }
                        if (needMergeAudioFile) {
                            clyPlayContainer.visible()
                        }
                        v.setBackgroundResource(R.drawable.shape_spb_30r_bg)
                        //录音
                        RecorderManager.getInstance().stopRecord()
                    }
                }
                true
            }
            //文字输入监听
            etInputContent.addTextChangedListener(object : SnailTextWatcher() {
                override fun onTextChanged(
                    s: CharSequence?,
                    start: Int,
                    before: Int,
                    count: Int
                ) {
                    // 输入框不为空时显示删除按钮，否则隐藏
                    if (s.isNullOrEmpty()) {
                        ivSendMsg.setBackgroundResource(R.drawable.svg_arrow_up_gray)
                    } else {
                        ivSendMsg.setBackgroundResource(R.drawable.svg_arrow_up)
                    }
                }
            })
            //发送消息
            ivSendMsg.singleClick {
                val content = etInputContent.text.toString().trim()
                if (content.isEmpty()) {
                    return@singleClick
                }
                evaluateListView.addData(
                    HomeworkDetailsItemInfo(
                        resourceType = "TEXT",
                        messageType = "TEACHER",
                        resource = content,
                        avatar = teacherInfo?.avatar
                    )
                )
                etInputContent.text?.clear()
            }
            //选择相册
            ivChoosePic.singleClick {
                ChoosePicBottomDialog().apply {
                    takePhotoListener = object : ChoosePicBottomDialog.TakePhotoListener {
                        override fun photo(path: List<String>) {
                            val list = mutableListOf<HomeworkDetailsItemInfo>()
                            path.forEach {
                                list.add(
                                    HomeworkDetailsItemInfo(
                                        resourceType = checkResourceType(it),
                                        messageType = "TEACHER",
                                        resource = it,
                                        avatar = teacherInfo?.avatar
                                    )
                                )
                            }
                            evaluateListView.addListData(list)
                        }
                    }
                }.show(childFragmentManager, "")
            }
            //播放合成的音频
            ivPlayControl.singleClick {
                when (AudioPlayerManager.playState) {
                    PlayState.FINISH,
                    PlayState.IDLE -> {
                        mergeAudioPath?.let { it1 ->
                            AudioPlayerManager.play(it1, playSpeed)
                        }
                    }

                    PlayState.PAUSE -> {
                        AudioPlayerManager.resume()
                    }

                    PlayState.PLAYING -> {
                        AudioPlayerManager.pause()
                    }

                    PlayState.READY -> {}
                    PlayState.BUFFERING -> {}
                    PlayState.STOP -> {}
                    PlayState.ERROR -> {}
                }
            }
            //点击播放速度
            vSpeedBg.singleClick {
                speedBottomDialog = SpeedBottomDialog("${playSpeed}x") {
                    playSpeed = it
                    AudioPlayerManager.setSpeed(it)
                    TeacherStorage.me.setPlaySpeed(it)
                    tvSpeed.text = "${it}x"
                }
                speedBottomDialog?.show(childFragmentManager, "")
            }
            //取消AI点评数据
            tvCancelAi.singleClick {
                clearAiCorrectingData()
            }
            //生成AI点评
            tvAiCorrecting.singleClick {
                if (selectAiList.isNotEmpty()) {
                    teacherHomeworkViewModel.autoCorrectingContent(selectAiList)
                }
            }
            //重试音频合并
            tvRetry.singleClick {
                mergeAudioFile()
            }

            if (recordingStateListener == null) {
                recordingStateListener = object : RecordingStateListener {
                    override fun recordStart() {
                        startRecordTime = System.currentTimeMillis()
                    }

                    override fun recordStop() {
                        endRecordTime = System.currentTimeMillis()
                    }

                    override fun recordCompleted(path: String?, duration: Int?) {
                        val time = System.currentTimeMillis() - startRecordTime
                        if (time < 500) {
                            ToastUtils.showShort("录音时间过短")
                            return
                        }
                        val temp =
                            HomeworkDetailsItemInfo(
                                resourceType = "AUDIO",
                                messageType = "TEACHER",
                                resource = path,
                                audioLength = ceil((time / 1000f)).toInt(),
                                avatar = teacherInfo?.avatar
                            )
                        evaluateListView.addData(temp)
                        lottieRecord.apply {
                            gone()
                            pauseAnimation()
                        }
                    }

                    override fun recordError(error: RecordErrorInfo) {
                    }
                }
            }
            recordingStateListener?.let {
                RecorderManager.getInstance().setRecordingStateListener(it)
            }
            evaluateListView.listener = object : ItemClickListener {
                override fun longClick(index: Int, info: HomeworkDetailsItemInfo) {
                    val correctingStatus =
                        teacherHomeworkViewModel.homeworkDetailsDataLiveData.value?.correctingStatus
                    if (correctingStatus == "PENDING") {
                        DeleteTipsDialog {
                            binding.evaluateListView.removeData(index, info)
                            //删除这条评论
                        }.show(childFragmentManager, "")
                    }
                }

                override fun lookLargeImage(info: String?) {
                    LookLargeImageDialog(info).show(childFragmentManager, "")
                }

                override fun select(isChecked: AppCompatCheckBox, info: HomeworkDetailsItemInfo) {
                    dealSelectUi(isChecked, info)
                }
            }
            seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(
                    seekBar: SeekBar?,
                    progress: Int,
                    fromUser: Boolean
                ) {
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {}

                override fun onStopTrackingTouch(seekBar: SeekBar?) {
                    seekBar?.progress?.let { progress ->
                        AudioPlayerManager.seekTo(progress)
                    }
                }
            })
            interceptBack()
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()
        dynamicBottomLayout()
        AudioPlayerManager.apply {
            addPlayProgressListener(playProgressListener)
            addPlayTimeListener(playTimeListener)
            addPlayStateListener(playStateListener)
            checkPlaybackPosition(100)
        }
        binding.tvSpeed.text = "${playSpeed}x"
    }

    /**
     * 清除AI点评数据
     */
    private fun clearAiCorrectingData() {
        selectCheckBoxList.forEach {
            it.isChecked = false
        }
        selectAiList.forEach {
            it.cbChecked = false
        }
        selectAiList.clear()
        selectCheckBoxList.clear()
        showSelectUi()
    }

    /**
     * 显示需要进行AI点评的UI
     */
    private fun dealSelectUi(cb: AppCompatCheckBox, info: HomeworkDetailsItemInfo) {
        if (cb.isChecked) {
            selectAiList.add(info)
            selectCheckBoxList.add(cb)
        } else {
            selectAiList.remove(info)
            selectCheckBoxList.remove(cb)
        }
        showSelectUi()
    }

    @SuppressLint("SetTextI18n")
    private fun showSelectUi() {
        if (selectAiList.isEmpty()) {
            binding.apply {
                ivKeyboardOrRecord.visible()
                flyBottomContainer.visible()
                ivChoosePic.visible()
                tvSelectItemTips.gone()
                tvSelectContent.gone()
                tvAiCorrecting.gone()
                tvCancelAi.gone()
            }
        } else {
            binding.apply {
                val content = selectAiList.map { it.originText }.joinToString(separator = "、")
                ivKeyboardOrRecord.gone()
                flyBottomContainer.gone()
                ivChoosePic.gone()
                tvSelectItemTips.visible()
                tvSelectContent.visible()
                tvAiCorrecting.visible()
                tvCancelAi.visible()
                tvSelectContent.text = content
                tvSelectItemTips.text = "已选择${selectAiList.size}条："
            }
        }
    }

    private fun dynamicBottomLayout() {
        binding.apply {
            if (teacherHomeworkViewModel.canEvaluate && !teacherHomeworkViewModel.isAiCorrecting) {
                vOpacity.visible()
                clyBottomContainer.visible()
                commonTitleView.setRightVisible(true)
                ivKeyboardOrRecord.visible()
                flyBottomContainer.visible()
                ivChoosePic.visible()
            } else if (teacherHomeworkViewModel.isAiCorrecting) {
                ivKeyboardOrRecord.gone()
                flyBottomContainer.gone()
                ivChoosePic.gone()
                commonTitleView.setRightVisible(false)
            } else {
                vOpacity.gone()
                lottieRecord.gone()
                clyBottomContainer.gone()
                commonTitleView.setRightVisible(false)
            }
        }
    }

    private fun checkResourceType(resource: String): String {
        if (resource.lowercase().contains(".mp4") ||
            resource.lowercase().contains(".mov")
        ) {
            return "VIDEO"
        }
        return "PIC"
    }

    private fun initUiInfo(info: HomeworkDetailsDataInfo) {
        homeworkDetailsDataInfo = info
        binding.apply {
            commonTitleView.setTitle(info.studentName ?: "")
            commonTitleView.setSubTitle("${info.courseTypeName}${info.courseStage}阶·第${info.courseNum}课·${info.courseworkTypeName}")
            info.items?.let {
                evaluateListView.setListData(handleQuoteData(it), isScrollToBottom = false)
            }
            if (info.correctingStatus == "PENDING") {
                commonTitleView.setRightVisible(true)
                clyBottomContainer.visible()
                binding.commonTitleView.setRight("提交")
                vOpacity.visible()
                llyContainer.visible()
                needMergeAudioFile =
                    info.items?.any { it.resourceType == "AUDIO" && it.messageType == "STUDENT_COURSEWORK" } == true
                if (needMergeAudioFile) {
                    mergeAudioFile()
                } else {
                    clyPlayContainer.gone()
                }
            } else if (teacherHomeworkViewModel.fromDubbing) {
                llyContainer.visible()
                lottieRecord.gone()
                needMergeAudioFile = true
                mergeAudioFile()
            } else {
                clyBottomContainer.gone()
                llyContainer.gone()
                vOpacity.gone()
                commonTitleView.setRightVisible(false)
            }
        }
    }

    /**
     * 合成音频
     */
    private fun mergeAudioFile() {
        homeworkDetailsDataInfo?.let { info ->
            val mergeAudioList = mutableListOf<String>()
            info.items?.forEach {
                if (it.messageType == "STUDENT_COURSEWORK" && it.resourceType == "AUDIO") {
                    it.resource?.getRealPath()?.let { url ->
                        mergeAudioList.add(url)
                    }
                }
            }
            if (mergeAudioList.isNotEmpty()) {
                val filePath =
                    AppConstants.getMergeAudioPath() + "/${mergeAudioList.toUniqueHash()}.mp3"
                if (!FileUtils.isFileExists(filePath)) {
                    FFmpegUtils.mergeAudioFiles(mergeAudioList, filePath, object : CommandCallback {
                        override fun start() {
                            showMergeAudioUi(1)
                        }

                        override fun succeed() {
                            mergeAudioPath = filePath
                            showMergeAudioUi(2)
                        }

                        override fun error(throwable: Throwable) {
                            ToastUtils.showShort("音频合并失败，${throwable.message}")
                            showMergeAudioUi(3)
                        }
                    })
                } else {
                    mergeAudioPath = filePath
                    showMergeAudioUi(2)
                }
            } else {
                binding.clyPlayContainer.gone()
            }
        }
    }

    /**
     * @param state 1:开始，2：成功，3：失败
     *
     */
    private fun showMergeAudioUi(state: Int) {
        if (!needMergeAudioFile) {
            return
        }
        ThreadUtils.runOnUiThread {
            binding.apply {
                when (state) {
                    1 -> {
                        llyLoadingContainer.visible()
                        vSpeedBg.gone()
                        ivDownTriangle.gone()
                        tvSpeed.gone()
                        ivPlayControl.gone()
                        seekBar.gone()
                        tvCurrentTime.gone()
                        tvTotalTime.gone()
                        llyErrorContainer.gone()
                    }

                    2 -> {
                        vSpeedBg.visible()
                        ivDownTriangle.visible()
                        tvSpeed.visible()
                        ivPlayControl.visible()
                        seekBar.visible()
                        tvCurrentTime.visible()
                        tvTotalTime.visible()
                        llyLoadingContainer.gone()
                        llyErrorContainer.gone()
                    }

                    3 -> {
                        llyLoadingContainer.gone()
                        vSpeedBg.gone()
                        ivDownTriangle.gone()
                        tvSpeed.gone()
                        ivPlayControl.gone()
                        seekBar.gone()
                        tvCurrentTime.gone()
                        tvTotalTime.gone()
                        llyErrorContainer.visible()
                    }
                }
            }
        }
    }

    /**
     * 处理引用数据
     */
    private fun handleQuoteData(list: List<HomeworkDetailsItemInfo>): List<ISame> {
        val resultList = mutableListOf<ISame>()
        list.forEach { data ->
            resultList.add(data)
            if (!data.refCourseworkId.isNullOrEmpty()) {
                list.firstOrNull { it.id == data.refCourseworkId }?.let {
                    resultList.add(it.toQuoteContentInfo("TEACHER"))
                }
            }
        }
        rawHomeworkDetailsDataList.clear()
        rawHomeworkDetailsDataList.addAll(list)
        return resultList
    }

    /**
     * 处理引用数据，从原来的数据列表中取
     */
    private fun handleQuoteDataFromRawData(list: List<HomeworkDetailsItemInfo>): List<ISame> {
        val resultList = mutableListOf<ISame>()
        list.forEach { data ->
            resultList.add(data)
            if (!data.refCourseworkId.isNullOrEmpty()) {
                rawHomeworkDetailsDataList.firstOrNull { it.id == data.refCourseworkId }?.let {
                    resultList.add(it.toQuoteContentInfo("TEACHER"))
                }
            }
        }
        return resultList
    }

    override fun onRetry() {
        super.onRetry()
        teacherHomeworkViewModel.getHomeworkDetails(StateType.PAGE)
    }

    /**
     * 拦截返回事件
     */
    private fun interceptBack() {
        requireActivity().onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    back(binding.commonTitleView)
                }
            }
        )
    }

    /**
     * 检查权限
     */
    private fun checkPermission(): Boolean {
        val ctx = context ?: return false
        return XXPermissions.isGranted(ctx, Permission.RECORD_AUDIO)
    }

    /**
     * 请求权限
     */
    private fun requestPermission() {
        context?.let { ctx ->
            XXPermissions.with(ctx).permission(Permission.RECORD_AUDIO)
                .request { _, _ ->
                }
        }
    }

    override fun onStop() {
        //录音
        RecorderManager.getInstance().stopRecord()
        AudioPlayManager.getInstance().stop()
        AudioPlayerManager.stop()
        super.onStop()
    }

    override fun onDestroy() {
        binding.evaluateListView.removePlayState()
        binding.evaluateListView.clean()
        AudioPlayManager.getInstance().release()
        AudioPlayerManager.apply {
            removePlayProgressListener(playProgressListener)
            removePlayTimeListener(playTimeListener)
            removePlayStateListener(playStateListener)
            removeTimerListener()
            stopCheckingPlaybackPosition()
        }
        AudioPlayerManager.release()
        super.onDestroy()
    }

    private fun back(view: View) {
        kotlin.runCatching {
            val pop = view.findNavController().popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
    }
}