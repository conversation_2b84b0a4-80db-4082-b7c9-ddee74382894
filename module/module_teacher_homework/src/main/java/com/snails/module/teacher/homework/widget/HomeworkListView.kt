package com.snails.module.teacher.homework.widget

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.teacher.HomeworkListItemInfo
import com.snails.base.network.repository.info.teacher.TeacherReviewedItemInfo
import com.snails.module.teacher.homework.R
import com.snails.module.teacher.homework.viewbinder.HomeworkItemViewBinder

/**
 * @Description 作业清单
 * <AUTHOR>
 * @CreateTime 2025年01月09日11:11:18
 */

class HomeworkListView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val albumList = mutableListOf<HomeworkListItemInfo>()
    var itemClickListener: ItemClickListener? = null
    private val listAdapter = MultiTypeAdapter()

    init {
        initView()
    }

    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(HomeworkListItemInfo::class.java, HomeworkItemViewBinder())
        }
        adapter = listAdapter
        this.addItemDecoration(ItemDecoration())
    }

    @Suppress("UNCHECKED_CAST")
    fun setData(new: List<HomeworkListItemInfo>, clean: Boolean) {
        if (clean) {
            albumList.clear()
        }
        albumList.addAll(new)
        val old = listAdapter.items as List<HomeworkListItemInfo>
        val diffResult: DiffUtil.DiffResult = DiffUtil.calculateDiff(object : DiffUtil.Callback() {
            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].javaClass == albumList[newItemPosition].javaClass
            }

            override fun getOldListSize(): Int {
                return old.size
            }

            override fun getNewListSize(): Int {
                return albumList.size
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                return old[oldItemPosition].isSame(albumList[newItemPosition])
            }
        })
        listAdapter.items = albumList
        diffResult.dispatchUpdatesTo(listAdapter)
    }


    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State,
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.bottom = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_12)
        }
    }

    interface ItemClickListener {
        fun itemClick(contentId: TeacherReviewedItemInfo)
    }
}