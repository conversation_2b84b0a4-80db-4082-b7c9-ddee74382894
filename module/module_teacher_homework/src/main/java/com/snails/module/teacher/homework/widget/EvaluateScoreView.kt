package com.snails.module.teacher.homework.widget

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.widget.RadioGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.snails.common.widget.ratingbar.BaseRatingBar
import com.snails.module.teacher.homework.R

/**
 * @Description 点评打分View
 * <AUTHOR>
 * @CreateTime 2025年02月24日 18:40:25
 */
class EvaluateScoreView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var rgGroup: RadioGroup? = null
    private var rbQx: BaseRatingBar? = null
    private var rbLld: BaseRatingBar? = null
    private var rbJcjl: BaseRatingBar? = null
    private var rbYfqy: BaseRatingBar? = null
    private var rbQjqy: BaseRatingBar? = null

    private var averageScore: String? = null //综合评价
    private var qxScore = 0 //情绪得分
    private var lldScore = 0 //流利度得分
    private var jcjlScore = 0 //基础积累得分
    private var yfqyScore = 0 //语法迁移得分
    private var qjqyScore = 0 //情景迁移得分

    var scoreListener: ScoreListener? = null

    init {
        // 使用 LayoutInflater 加载布局
        val layoutId = R.layout.layout_evaluate_score_view
        val view = LayoutInflater.from(context).inflate(layoutId, this, true)

        rgGroup = view.findViewById(R.id.rgGroup)

        rbQx = view.findViewById(R.id.rbQx)
        rbLld = view.findViewById(R.id.rbLld)
        rbJcjl = view.findViewById(R.id.rbJcjl)
        rbYfqy = view.findViewById(R.id.rbYfqy)
        rbQjqy = view.findViewById(R.id.rbQjqy)

        initClick()
    }

    private fun initClick() {
        rgGroup?.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rbSSS -> {
                    averageScore = "SSS"
                    updateScore()
                }

                R.id.rbS -> {
                    averageScore = "S"
                    updateScore()
                }

                R.id.rbA -> {
                    averageScore = "A"
                    updateScore()
                }

                R.id.rbB -> {
                    averageScore = "B"
                    updateScore()
                }
            }
        }
        rbQx?.setOnRatingChangeListener { _, rating, _ ->
            //情绪
            qxScore = rating.toInt()
            updateScore()
        }
        rbLld?.setOnRatingChangeListener { _, rating, _ ->
            //流利度
            lldScore = rating.toInt()
            updateScore()
        }
        rbJcjl?.setOnRatingChangeListener { _, rating, _ ->
            //基础积累
            jcjlScore = rating.toInt()
            updateScore()
        }
        rbYfqy?.setOnRatingChangeListener { _, rating, _ ->
            //语法迁移
            yfqyScore = rating.toInt()
            updateScore()
        }
        rbQjqy?.setOnRatingChangeListener { _, rating, _ ->
            //情景迁移
            qjqyScore = rating.toInt()
            updateScore()
        }
    }

    private fun updateScore() {
        scoreListener?.score(
            averageScore,
            qxScore,
            lldScore,
            jcjlScore,
            yfqyScore,
            qjqyScore
        )
    }

    interface ScoreListener {
        fun score(
            averageScore: String? = null, //综合评价
            qxScore: Int = 0, //情绪得分
            lldScore: Int = 0, //流利度得分
            jcjlScore: Int = 0, //基础积累得分
            yfqyScore: Int = 0, //语法迁移得分
            qjqyScore: Int = 0, //情景迁移得分
        )
    }
}