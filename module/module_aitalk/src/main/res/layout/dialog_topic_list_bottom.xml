<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_459"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_top20r_sbw_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="@dimen/base_sw_dp_44"
        android:layout_height="@dimen/base_sw_dp_44"
        android:layout_marginTop="@dimen/base_sw_dp_4"
        android:background="@drawable/svg_dialog_small_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_15"
        android:text="@string/str_topic_title"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/flyContainer"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_0"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refreshLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:srlEnableAutoLoadMore="false"
            app:srlEnableNestedScrolling="false"
            app:srlEnableRefresh="false">

            <com.snails.module.aitalk.widget.TopicListBottomView
                android:id="@+id/topicListBottomView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

            <com.snails.module.base.widget.refresh.CommonRefresh
                android:id="@+id/footer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
