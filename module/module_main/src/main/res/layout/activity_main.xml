<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".MainActivity">

    <!--NavHost-->
    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/fragmentContainerView"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/llyPlayContainer"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_52"
        android:background="@color/surface_background_white"
        android:gravity="center_vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/sivAlbumPic"
            android:layout_width="@dimen/base_sw_dp_36"
            android:layout_height="@dimen/base_sw_dp_36"
            android:layout_marginStart="@dimen/base_sw_dp_12"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:shapeAppearance="@style/CircleStyle" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvStoryName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_8"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:marqueeRepeatLimit="marquee_forever"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:textColor="@color/text_body"
            android:textSize="@dimen/text_body_medium"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivPlayState"
            app:layout_constraintStart_toEndOf="@+id/sivAlbumPic"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="小猪佩奇 01：小猪的奇遇" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivPlayState"
            android:layout_width="@dimen/base_sw_dp_32"
            android:layout_height="@dimen/base_sw_dp_32"
            android:layout_marginHorizontal="@dimen/base_sw_dp_16"
            android:background="@drawable/svg_play_pause"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivClose"
            app:layout_constraintStart_toEndOf="@+id/tvStoryName"
            app:layout_constraintTop_toTopOf="parent" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottieProgress"
            android:layout_width="@dimen/base_sw_dp_32"
            android:layout_height="@dimen/base_sw_dp_32"
            android:layout_marginHorizontal="@dimen/base_sw_dp_16"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivClose"
            app:layout_constraintStart_toEndOf="@+id/tvStoryName"
            app:layout_constraintTop_toTopOf="parent"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/lottie_audio_loading" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivClose"
            android:layout_width="@dimen/base_sw_dp_24"
            android:layout_height="@dimen/base_sw_dp_24"
            android:layout_marginEnd="@dimen/base_sw_dp_12"
            android:background="@drawable/svg_close_24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_1"
            android:background="@color/border_divider"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.snails.module.main.widget.tab.BottomNavigationView
        android:id="@+id/bottomNavigationView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_56"
        android:background="@color/surface_background_white" />
</LinearLayout>
