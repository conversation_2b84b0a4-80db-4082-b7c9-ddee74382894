<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/base_sw_dp_16"
    android:background="@drawable/shape_white_bg_radius_16"
    android:paddingBottom="@dimen/base_sw_dp_12">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvExChangeCourse"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:background="@drawable/shape_study_course_exchange_bg"
        android:drawableEnd="@drawable/svg_exchange_blue"
        android:drawablePadding="@dimen/base_sw_dp_4"
        android:gravity="center"
        android:textColor="@color/text_link"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="第1课" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivCoursePic"
        android:layout_width="@dimen/base_sw_dp_88"
        android:layout_height="@dimen/base_sw_dp_88"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearance="@style/Rounded8Style"
        tools:background="@color/text_describe" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCourseName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:gravity="center"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h3"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvExChangeCourse"
        tools:text="课程名称" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCourseVocabulary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:gravity="center"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvCourseName"
        tools:text="词汇·300" />

    <View
        android:id="@+id/vCourseLine"
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:background="@color/border_divider"
        app:layout_constraintBottom_toBottomOf="@+id/tvCourseVocabulary"
        app:layout_constraintStart_toEndOf="@+id/tvCourseVocabulary"
        app:layout_constraintTop_toTopOf="@+id/tvCourseVocabulary" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCoursePhrase"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:gravity="center"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintStart_toEndOf="@+id/vCourseLine"
        app:layout_constraintTop_toBottomOf="@+id/tvCourseName"
        tools:text="词汇·300" />

    <View
        android:id="@+id/vLine"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        android:background="@color/border_divider"
        app:layout_constraintTop_toBottomOf="@+id/sivCoursePic" />

    <com.snails.module.main.widget.StudyStepView
        android:id="@+id/studyStepView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_20"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        app:layout_constraintTop_toBottomOf="@+id/vLine" />
</androidx.constraintlayout.widget.ConstraintLayout>
