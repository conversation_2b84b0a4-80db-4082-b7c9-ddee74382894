<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/llyNoBuy"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="@dimen/base_sw_dp_140"
            android:layout_height="@dimen/base_sw_dp_108"
            android:background="@drawable/svg_study_state" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_10"
            android:text="@string/str_no_buy_course_tips"
            android:textColor="@color/text_body"
            android:textSize="@dimen/text_body_medium" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvGoBuyGoods"
            android:layout_width="@dimen/base_sw_dp_110"
            android:layout_height="@dimen/base_sw_dp_32"
            android:layout_marginTop="@dimen/base_sw_dp_12"
            android:background="@drawable/shape_spb_27r_bg"
            android:gravity="center"
            android:text="@string/str_go_buy_course"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/text_body_medium"
            android:textStyle="bold" />
    </LinearLayout>

    <View
        android:id="@+id/vStatus"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_44"
        android:background="@drawable/shape_study_status_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/flyLevelContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_study_head_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vStatus">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/ivStudyLevel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_16"
            android:drawableEnd="@drawable/svg_down"
            android:drawablePadding="@dimen/base_sw_dp_2"
            android:paddingTop="@dimen/base_sw_dp_20"
            android:paddingBottom="@dimen/base_sw_dp_20"
            android:textColor="@color/text_on_primary_button"
            android:textSize="@dimen/headline_h1"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="听力·1阶" />
    </FrameLayout>

    <com.snails.module.main.widget.StudyListView
        android:id="@+id/studyListView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_0"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/flyLevelContainer" />
</androidx.constraintlayout.widget.ConstraintLayout>