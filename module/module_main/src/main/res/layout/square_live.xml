<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/base_sw_dp_16"
    android:background="@drawable/shape_white_bg_radius_16"
    android:paddingBottom="@dimen/base_sw_dp_12">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStudyCourse"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h3"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="学习课程" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivLive"
        android:layout_width="@dimen/base_sw_dp_88"
        android:layout_height="@dimen/base_sw_dp_132"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvStudyCourse"
        app:shapeAppearance="@style/Rounded8Style"
        tools:background="@color/text_body" />

    <com.snails.base.video_player.control.EmptyControlVideo
        android:id="@+id/gsyVideoPlayer"
        android:layout_width="@dimen/base_sw_dp_88"
        android:layout_height="@dimen/base_sw_dp_132"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvStudyCourse" />

    <View
        android:id="@+id/vLive"
        android:layout_width="@dimen/base_sw_dp_88"
        android:layout_height="@dimen/base_sw_dp_132"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvStudyCourse" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvWatchLiveNum"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/base_sw_dp_6"
        android:background="@drawable/shape_watch_live_num_bg"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/text_on_primary_button"
        android:textSize="@dimen/text_body_tiny"
        app:layout_constraintEnd_toEndOf="@+id/ivLive"
        app:layout_constraintStart_toStartOf="@+id/ivLive"
        app:layout_constraintTop_toTopOf="@+id/ivLive" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLiveName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintStart_toEndOf="@+id/ivLive"
        app:layout_constraintTop_toTopOf="@+id/ivLive"
        tools:text="学习课程" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLiveDesc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_3"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintStart_toEndOf="@+id/ivLive"
        app:layout_constraintTop_toBottomOf="@+id/tvLiveName"
        tools:text="先听说后读写，精泛相结合" />

    <View
        android:id="@+id/vCourseBg"
        android:layout_width="@dimen/base_sw_dp_0"
        android:layout_height="@dimen/base_sw_dp_0"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        android:background="@drawable/shape_course_bg"
        app:layout_constraintBottom_toBottomOf="@+id/ivCoursePic"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/ivCoursePic"
        app:layout_constraintTop_toTopOf="@+id/ivCoursePic" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivCoursePic"
        android:layout_width="@dimen/base_sw_dp_78"
        android:layout_height="@dimen/base_sw_dp_78"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_6"
        android:scaleType="centerCrop"
        app:layout_constraintStart_toEndOf="@+id/ivLive"
        app:layout_constraintTop_toBottomOf="@+id/tvLiveDesc"
        app:shapeAppearance="@style/Rounded8Style"
        tools:background="@color/text_body" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCourseName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_3"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_medium"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/vCourseBg"
        app:layout_constraintStart_toEndOf="@+id/ivCoursePic"
        app:layout_constraintTop_toTopOf="@+id/ivCoursePic"
        tools:text="浅爸训练营浅爸训练营" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCourseDesc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_3"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintEnd_toEndOf="@+id/vCourseBg"
        app:layout_constraintStart_toEndOf="@+id/ivCoursePic"
        app:layout_constraintTop_toBottomOf="@+id/tvCourseName"
        tools:text="30天英语体验课程30天英语体验课程" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCoursePrice"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_4"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_error"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="@+id/vCourseBg"
        app:layout_constraintStart_toEndOf="@+id/ivCoursePic"
        app:layout_constraintTop_toBottomOf="@+id/tvCourseDesc"
        tools:text="399.00" />

</androidx.constraintlayout.widget.ConstraintLayout>