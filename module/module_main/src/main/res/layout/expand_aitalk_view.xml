<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/base_sw_dp_16"
    android:layout_marginTop="@dimen/base_sw_dp_24"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAiTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:maxLines="1"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h3"
        android:textStyle="bold"
        tools:text="AI练口语" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_12">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivAiTalkPic"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTopicName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_12"
            android:layout_marginTop="@dimen/base_sw_dp_40"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/headline_h3"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.5"
            tools:text="暑假最开心的事暑假最开心的事暑假最开心的事" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvGoPractise"
            android:layout_width="@dimen/base_sw_dp_109"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_12"
            android:layout_marginTop="@dimen/base_sw_dp_12"
            android:background="@drawable/shape_to_to_practise_bg"
            android:gravity="center"
            android:text="@string/str_go_to_practise"
            android:textColor="#1983CB"
            android:textSize="@dimen/text_body_medium"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTopicName" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.appcompat.widget.LinearLayoutCompat>