<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivStudyState"
        android:layout_width="@dimen/base_sw_dp_21"
        android:layout_height="@dimen/base_sw_dp_20"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@drawable/svg_lesson_in_progress" />

    <View
        android:id="@+id/vLessonState"
        android:layout_width="@dimen/base_sw_dp_0"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/base_sw_dp_20"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivStudyState"
        tools:background="@drawable/shape_study_lesson_complete" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLessonName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/studyStepViewScore"
        app:layout_constraintStart_toStartOf="@+id/vLessonState"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Step2"
        tools:textColor="@color/text_headline" />

    <com.snails.module.main.widget.StudyStepViewScore
        android:id="@+id/studyStepViewScore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>