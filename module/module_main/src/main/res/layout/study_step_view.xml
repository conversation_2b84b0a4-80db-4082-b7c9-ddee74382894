<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.snails.module.base.widget.DashedLineView
        android:id="@+id/dashedLineView"
        android:layout_width="@dimen/base_sw_dp_1"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/base_sw_dp_17"
        android:layout_marginTop="@dimen/base_sw_dp_32"
        android:layout_marginBottom="@dimen/base_sw_dp_32"
        app:dashColor="@color/border_divider"
        app:dashGap="@dimen/base_sw_dp_3"
        app:dashLength="@dimen/base_sw_dp_3"
        app:layout_constraintBottom_toBottomOf="@+id/lessonSix"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/lessonOne" />

    <com.snails.module.main.widget.StudyStepItemView
        android:id="@+id/lessonOne"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_7"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.snails.module.main.widget.StudyStepItemView
        android:id="@+id/lessonTwo"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_7"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lessonOne" />

    <com.snails.module.main.widget.StudyStepItemView
        android:id="@+id/lessonThree"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_7"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lessonTwo" />

    <com.snails.module.main.widget.StudyStepItemView
        android:id="@+id/lessonFour"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_7"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lessonThree" />

    <com.snails.module.main.widget.StudyStepItemView
        android:id="@+id/lessonFive"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_7"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lessonFour" />

    <com.snails.module.main.widget.StudyStepItemView
        android:id="@+id/lessonSix"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_7"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lessonFive" />
</androidx.constraintlayout.widget.ConstraintLayout>