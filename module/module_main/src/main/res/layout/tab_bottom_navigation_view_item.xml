<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    tools:viewBindingIgnore="true">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivItemPic"
        android:layout_width="@dimen/base_sw_dp_32"
        android:layout_height="@dimen/base_sw_dp_32" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvItemTxt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_2"
        android:textColor="@color/tab_bottom_item_state_selector"
        android:textSize="@dimen/text_body_tiny" />
</androidx.appcompat.widget.LinearLayoutCompat>