<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_transparent"
    android:elevation="@dimen/base_sw_dp_10">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_47"
        android:background="@drawable/shape_study_marking_info_bg" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clyContainer"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_80"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:layout_marginBottom="@dimen/base_sw_dp_1"
        android:background="@drawable/shape_white_bg_radius_16">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivStudyTeacher"
            android:layout_width="@dimen/base_sw_dp_55"
            android:layout_height="@dimen/base_sw_dp_55"
            android:layout_marginStart="@dimen/base_sw_dp_12"
            android:background="@drawable/svg_teacher_head"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvStudyOpr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_10"
            android:text="@string/str_my_homework"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/headline_h4"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@+id/ivStudyTeacher"
            app:layout_constraintTop_toTopOf="@+id/ivStudyTeacher" />

        <View
            android:id="@+id/vCirclePoint"
            android:layout_width="@dimen/base_sw_dp_6"
            android:layout_height="@dimen/base_sw_dp_6"
            android:layout_marginStart="@dimen/base_sw_dp_10"
            android:background="@drawable/shape_study_new_message"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tvStudyMessage"
            app:layout_constraintStart_toEndOf="@+id/ivStudyTeacher"
            app:layout_constraintTop_toTopOf="@+id/tvStudyMessage" />

        <TextView
            android:id="@+id/tvStudyMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_7"
            android:layout_marginTop="@dimen/base_sw_dp_4"
            android:textColor="@color/text_describe"
            android:textSize="@dimen/text_body_small"
            app:layout_constraintStart_toEndOf="@+id/vCirclePoint"
            app:layout_constraintTop_toBottomOf="@+id/tvStudyOpr"
            app:layout_goneMarginStart="@dimen/base_sw_dp_10"
            tools:text="你有3条新点评" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="@dimen/base_sw_dp_16"
            android:layout_height="@dimen/base_sw_dp_16"
            android:layout_marginEnd="@dimen/base_sw_dp_12"
            android:background="@drawable/svg_arrow_right"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>