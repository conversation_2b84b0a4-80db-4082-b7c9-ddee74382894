<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clyContainer"
    android:layout_width="@dimen/base_sw_dp_136"
    android:layout_height="@dimen/base_sw_dp_248"
    android:layout_marginStart="@dimen/base_sw_dp_16"
    android:background="@color/color_transparent"
    android:orientation="vertical"
    tools:viewBindingIgnore="true">

    <View
        android:id="@+id/vOneBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_119"
        android:background="@drawable/shape_study_star_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivOne"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="@dimen/base_sw_dp_2"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="@+id/vOneBg"
        app:layout_constraintEnd_toEndOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="@+id/vOneBg"
        app:layout_constraintTop_toTopOf="@+id/vOneBg"
        app:shapeAppearance="@style/Rounded8Style"
        tools:background="@color/text_body" />

    <com.snails.module.base.widget.ResTypeView
        android:id="@+id/ivPlayOne"
        android:layout_width="@dimen/base_sw_dp_20"
        android:layout_height="@dimen/base_sw_dp_20"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:layout_marginEnd="@dimen/base_sw_dp_8"
        app:layout_constraintEnd_toEndOf="@+id/sivOne"
        app:layout_constraintTop_toTopOf="@+id/sivOne"
        tools:visibility="visible" />

    <View
        android:id="@+id/vTwoBg"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_119"
        android:background="@drawable/shape_study_star_bg"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        app:layout_constraintTop_toBottomOf="@+id/vOneBg"
        app:layout_constraintStart_toStartOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivTwo"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="@dimen/base_sw_dp_2"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="@+id/vTwoBg"
        app:layout_constraintEnd_toEndOf="@+id/vTwoBg"
        app:layout_constraintStart_toStartOf="@+id/vTwoBg"
        app:layout_constraintTop_toTopOf="@+id/vTwoBg"
        app:shapeAppearance="@style/Rounded8Style"
        tools:background="@color/text_body" />

    <com.snails.module.base.widget.ResTypeView
        android:id="@+id/ivPlayTwo"
        android:layout_width="@dimen/base_sw_dp_20"
        android:layout_height="@dimen/base_sw_dp_20"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:layout_marginEnd="@dimen/base_sw_dp_8"
        app:layout_constraintEnd_toEndOf="@+id/sivTwo"
        app:layout_constraintTop_toTopOf="@+id/sivTwo"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>