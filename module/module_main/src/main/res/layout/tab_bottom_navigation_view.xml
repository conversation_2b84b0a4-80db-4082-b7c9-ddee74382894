<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    tools:viewBindingIgnore="true">

    <com.snails.module.main.widget.tab.BottomNavigationItemView
        android:id="@+id/navSquare"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        app:labelText="@string/str_main_square"
        app:selectedIcon="@drawable/svg_tab_selected_square"
        app:unselectedIcon="@drawable/svg_tab_unselected_square" />

    <com.snails.module.main.widget.tab.BottomNavigationItemView
        android:id="@+id/navStudy"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        app:labelText="@string/str_main_study"
        app:selectedIcon="@drawable/svg_tab_selected_study"
        app:unselectedIcon="@drawable/svg_tab_unselected_study" />

    <com.snails.module.main.widget.tab.BottomNavigationItemView
        android:id="@+id/navExpand"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        app:labelText="@string/str_main_expand"
        app:selectedIcon="@drawable/svg_tab_selected_expand"
        app:unselectedIcon="@drawable/svg_tab_unselected_expand" />

    <com.snails.module.main.widget.tab.BottomNavigationItemView
        android:id="@+id/navMine"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        app:labelText="@string/str_main_mine"
        app:selectedIcon="@drawable/svg_tab_selected_mine"
        app:unselectedIcon="@drawable/svg_tab_unselected_mine" />

</androidx.appcompat.widget.LinearLayoutCompat>