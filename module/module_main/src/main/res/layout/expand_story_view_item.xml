<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clyContainer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.snails.base.complex_imageview.ComplexImageView
        android:id="@+id/civStoryPic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:scaleTypes="centerCrop"
        tools:background="@color/text_disable" />

    <com.snails.module.base.widget.ResTypeView
        android:id="@+id/ivPlay"
        android:layout_width="@dimen/base_sw_dp_20"
        android:layout_height="@dimen/base_sw_dp_20"
        android:layout_marginTop="@dimen/base_sw_dp_8"
        android:layout_marginEnd="@dimen/base_sw_dp_8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_36"
        android:background="@drawable/shape_sbw_bottom_12r_bg"
        app:layout_constraintTop_toBottomOf="@+id/civStoryPic" />

    <TextView
        android:id="@+id/sivStoryName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/base_sw_dp_8"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/civStoryPic"
        tools:text="标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题" />
</androidx.constraintlayout.widget.ConstraintLayout>