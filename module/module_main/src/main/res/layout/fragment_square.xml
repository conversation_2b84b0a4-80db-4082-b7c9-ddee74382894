<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/vStatus"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_44"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:gravity="center_vertical"
        android:paddingTop="@dimen/base_sw_dp_20"
        android:paddingBottom="@dimen/base_sw_dp_20"
        android:text="@string/str_app_name"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h1"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vStatus" />

    <com.snails.module.main.widget.SquareListView
        android:id="@+id/squareListView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/color_transparent"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>