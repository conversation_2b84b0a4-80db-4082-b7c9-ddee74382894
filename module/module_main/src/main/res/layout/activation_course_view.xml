<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_72"
    android:layout_marginHorizontal="@dimen/base_sw_dp_16"
    android:background="@drawable/shape_sbw_36r_bg"
    android:gravity="center_vertical"
    android:orientation="horizontal">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/v1"
        android:layout_width="@dimen/base_sw_dp_48"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginStart="@dimen/base_sw_dp_20"
        android:background="@drawable/svg_tv"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="课程兑换"
        android:textColor="#595C5F"
        android:textSize="@dimen/base_sw_sp_16"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/v1"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="HardcodedText" />

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="@dimen/base_sw_dp_14"
        android:layout_height="@dimen/base_sw_dp_14"
        android:layout_gravity="end"
        android:layout_marginEnd="@dimen/base_sw_dp_20"
        android:background="@drawable/svg_arrow_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>