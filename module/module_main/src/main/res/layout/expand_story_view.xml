<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_transparent">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h3"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="书架" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLookMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:drawableEnd="@drawable/svg_arrow_right"
        android:text="@string/str_look_more"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTitle" />

    <com.snails.module.main.widget.StoryItemListView
        android:id="@+id/storyItemListView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_10"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:layout_marginEnd="@dimen/base_sw_dp_10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />
</androidx.constraintlayout.widget.ConstraintLayout>