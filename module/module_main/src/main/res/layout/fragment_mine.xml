<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <View
        android:id="@+id/sivUserHeadBg"
        android:layout_width="@dimen/base_sw_dp_64"
        android:layout_height="@dimen/base_sw_dp_64"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_24"
        android:background="@drawable/shape_white_bg_circle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivUserHead"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="@dimen/base_sw_dp_2"
        android:background="@color/border_divider"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="@+id/sivUserHeadBg"
        app:layout_constraintEnd_toEndOf="@+id/sivUserHeadBg"
        app:layout_constraintStart_toStartOf="@+id/sivUserHeadBg"
        app:layout_constraintTop_toTopOf="@+id/sivUserHeadBg"
        app:shapeAppearance="@style/CircleStyle" />

    <FrameLayout
        android:id="@+id/flyName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toStartOf="@+id/tvUserCoin"
        app:layout_constraintStart_toEndOf="@+id/sivUserHeadBg"
        app:layout_constraintTop_toTopOf="@+id/sivUserHeadBg">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvUserName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/base_sw_dp_9"
            android:layout_marginTop="@dimen/base_sw_dp_4"
            android:drawableEnd="@drawable/svg_mine_pen"
            android:drawablePadding="@dimen/base_sw_dp_7"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/headline_h3"
            android:textStyle="bold"
            tools:text="小景" />
    </FrameLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUserLevel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_9"
        android:layout_marginTop="@dimen/base_sw_dp_4"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintStart_toEndOf="@+id/sivUserHeadBg"
        app:layout_constraintTop_toBottomOf="@+id/flyName"
        tools:text="听力一阶" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUserCoin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_9"
        android:layout_marginTop="@dimen/base_sw_dp_4"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:background="@drawable/shape_coin_bg"
        android:drawableStart="@drawable/svg_mine_coin"
        android:drawablePadding="@dimen/base_sw_dp_2"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@color/text_warn"
        android:textSize="@dimen/button_h1"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/sivUserHeadBg"
        tools:text="33" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mcvStudyInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_24"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:background="@drawable/shape_white_bg_radius_16"
        android:paddingBottom="@dimen/base_sw_dp_20"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sivUserHeadBg">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvUserStudyOverview"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_16"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:text="@string/str_mine_study_over_view"
            android:textColor="@color/text_headline"
            android:textSize="@dimen/headline_h4"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@+id/tvUserStudyReport"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvUserStudyReport" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvUserStudyReport"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/base_sw_dp_9"
            android:layout_marginTop="@dimen/base_sw_dp_15"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            android:background="@drawable/shape_study_report_bg"
            android:drawableStart="@drawable/svg_mine_study_report"
            android:drawablePadding="@dimen/base_sw_dp_2"
            android:gravity="center"
            android:maxLines="1"
            android:text="@string/str_mine_study_report"
            android:textColor="@color/text_link"
            android:textSize="@dimen/button_h1"
            android:visibility="invisible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/base_sw_dp_20"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvUserStudyReport">

            <RelativeLayout
                android:layout_width="@dimen/base_sw_dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvUserStudyPersistenceDays"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:maxLines="1"
                    android:textColor="@color/text_headline"
                    android:textSize="@dimen/headline_h2"
                    android:textStyle="bold"
                    tools:text="33" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvUserStudyPersistenceDaysTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tvUserStudyPersistenceDays"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/base_sw_dp_8"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:text="坚持学习(天)"
                    android:textColor="@color/text_headline"
                    android:textSize="@dimen/text_body_footnote" />
            </RelativeLayout>

            <View
                android:id="@+id/vLineOne"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_weight="0"
                android:background="@color/border_divider" />

            <RelativeLayout
                android:layout_width="@dimen/base_sw_dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvUserStudyPersistenceTimes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/text_headline"
                    android:textSize="@dimen/headline_h2"
                    android:textStyle="bold"
                    tools:text="33" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvUserStudyPersistenceTimesTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tvUserStudyPersistenceTimes"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/base_sw_dp_7"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:text="单词积累(词)"
                    android:textColor="@color/text_headline"
                    android:textSize="@dimen/text_body_footnote" />
            </RelativeLayout>

            <View
                android:id="@+id/vLineTwo"
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_weight="0"
                android:background="@color/border_divider" />

            <RelativeLayout
                android:layout_width="@dimen/base_sw_dp_0"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvUserExerciseCompletionTimes"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:gravity="center_vertical"
                    android:maxLines="1"
                    android:textColor="@color/text_headline"
                    android:textSize="@dimen/headline_h2"
                    android:textStyle="bold"
                    tools:text="33" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvUserExerciseCompletionTimesTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tvUserExerciseCompletionTimes"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="@dimen/base_sw_dp_7"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text="短语积累(句)"
                    android:textColor="@color/text_headline"
                    android:textSize="@dimen/text_body_footnote" />

            </RelativeLayout>
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivCoinMall"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_77"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_6"
        android:background="@drawable/svg_coin_mall"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@+id/ivInvitationCourtesy"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mcvStudyInfo" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivInvitationCourtesy"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_77"
        android:layout_marginStart="@dimen/base_sw_dp_6"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:background="@drawable/svg_invitation_courtesy"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivCoinMall"
        app:layout_constraintTop_toBottomOf="@+id/mcvStudyInfo" />

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_16"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:background="@drawable/shape_white_bg_radius_16"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivCoinMall">

        <com.snails.module.base.widget.CommonItemView
            android:id="@+id/civMessageNotification"
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_64"
            android:layout_marginStart="@dimen/base_sw_dp_16"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            app:itemIcon="@drawable/svg_message_notification"
            app:itemTitle="@string/str_mine_message_notification" />

        <com.snails.module.base.widget.CommonItemView
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_64"
            android:layout_marginStart="@dimen/base_sw_dp_16"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            android:visibility="gone"
            app:itemIcon="@drawable/svg_order_logistics"
            app:itemTitle="@string/str_mine_order_logistics" />

        <com.snails.module.base.widget.CommonItemView
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_64"
            android:layout_marginStart="@dimen/base_sw_dp_16"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            android:visibility="gone"
            app:itemIcon="@drawable/svg_contract_signing"
            app:itemTitle="@string/str_mine_contract_signing" />

        <com.snails.module.base.widget.CommonItemView
            android:id="@+id/civCustomerService"
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_64"
            android:layout_marginStart="@dimen/base_sw_dp_16"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            app:itemIcon="@drawable/svg_customer_service"
            app:itemTitle="@string/str_mine_customer_service" />

        <com.snails.module.base.widget.CommonItemView
            android:id="@+id/civSetting"
            android:layout_width="match_parent"
            android:layout_height="@dimen/base_sw_dp_64"
            android:layout_marginStart="@dimen/base_sw_dp_16"
            android:layout_marginEnd="@dimen/base_sw_dp_16"
            app:itemIcon="@drawable/svg_setting"
            app:itemTitle="@string/str_mine_setting" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.constraintlayout.widget.ConstraintLayout>