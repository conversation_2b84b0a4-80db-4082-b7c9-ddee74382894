<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_transparent"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStudyStarTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h3"
        android:textStyle="bold"
        tools:text="学习之星" />

    <com.snails.module.main.widget.StudyStarView
        android:id="@+id/studyStarView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_246"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:background="@color/color_transparent"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@+id/tvWonderfulTitle"
        tools:listitem="@layout/square_study_star_item" />
</androidx.appcompat.widget.LinearLayoutCompat>