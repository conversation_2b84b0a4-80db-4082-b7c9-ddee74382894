<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:tools="http://schemas.android.com/tools"
    android:width="166dp"
    android:height="77dp"
    android:viewportWidth="166"
    android:viewportHeight="77">
    <group>
        <clip-path android:pathData="M12,0L153.5,0A12,12 0,0 1,165.5 12L165.5,65A12,12 0,0 1,153.5 77L12,77A12,12 0,0 1,0 65L0,12A12,12 0,0 1,12 0z" />
        <path android:pathData="M12,0L153.5,0A12,12 0,0 1,165.5 12L165.5,65A12,12 0,0 1,153.5 77L12,77A12,12 0,0 1,0 65L0,12A12,12 0,0 1,12 0z">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:endX="74"
                    android:endY="-32.5"
                    android:startX="156.5"
                    android:startY="77"
                    android:type="linear">
                    <item
                        android:color="#FF34AAFE"
                        android:offset="0" />
                    <item
                        android:color="#FF4CB6FF"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path
            android:fillColor="#ffffff"
            android:pathData="M17.28,16.33H19.02V19.16H21.25V26.97H18.99V29.97L19.87,29.72C19.72,29.07 19.56,28.46 19.4,27.85L20.8,27.36C21.18,28.77 21.54,30.37 21.87,32.21L20.43,32.55C20.35,32.12 20.28,31.7 20.21,31.31C18.59,31.81 16.88,32.24 15.12,32.6L14.68,30.86C15.55,30.73 16.39,30.59 17.24,30.41V26.97H15.04V19.16H17.28V16.33ZM17.4,20.81H16.65V25.31H17.4V20.81ZM18.9,25.31H19.63V20.81H18.9V25.31ZM22.75,16.82H30.02V21.87H27.32C27.3,22.38 27.28,22.86 27.25,23.31H30.79V30.73C30.79,32.1 30.22,32.78 29.07,32.78H27.48L27.01,31.04L28.49,31.13C28.85,31.13 29.03,30.86 29.03,30.33V29.2L28.27,29.97C27.82,29.16 27.23,28.32 26.51,27.44C26.08,28.57 25.43,29.56 24.53,30.39L23.83,29.54V32.82H22.06V23.31H25.59C25.63,22.85 25.65,22.38 25.66,21.87H22.75V16.82ZM25.41,24.99H23.83V28.59C24.58,27.69 25.07,26.66 25.32,25.49C25.36,25.31 25.38,25.15 25.41,24.99ZM29.03,28.07V24.99H27.09L26.98,25.65C27.77,26.48 28.45,27.27 29.03,28.07ZM28.26,20.22V18.47H24.51V20.22H28.26ZM35.98,21.64C35.37,22.83 34.7,23.85 34,24.75L32.72,23.21C33.96,21.71 35.06,19.59 36,16.82L37.78,17.23C37.47,18.15 37.15,19.01 36.82,19.8H40.48V16.17H42.37V19.8H48.22V21.64H42.37V25.56H49.21V27.42H42.37V32.8H40.48V27.42H32.97V25.56H40.48V21.64H35.98ZM64.09,30.71H62.38L61.9,28.82L63.46,28.91C63.81,28.91 63.99,28.71 63.99,28.35V23.22H59.99V32.91H58.08V23.22H54.07V30.75H52.2V21.42H58.08V19.16C56.1,19.28 53.98,19.35 51.67,19.35L51.08,17.59C56.82,17.59 61.68,17.14 65.68,16.28L66.63,17.91C64.72,18.38 62.51,18.74 59.99,18.99V21.42H65.86V28.77C65.86,30.06 65.26,30.71 64.09,30.71ZM73.4,26.27C73.09,26.48 72.79,26.68 72.5,26.86L71.46,25.51V32.75H69.64V20.97H73.35C73.11,20.36 72.86,19.8 72.57,19.26H68.95V17.46H75.94C75.79,17.07 75.63,16.69 75.47,16.35L77.52,16.02C77.7,16.46 77.86,16.94 78.01,17.46H85.05V19.26H81.28C81.03,19.88 80.76,20.45 80.47,20.97H84.33V30.6C84.33,32.04 83.7,32.78 82.47,32.78H80.64L80.17,31.09L81.9,31.16C82.29,31.16 82.51,30.84 82.51,30.21V25.53L81.19,26.82C81,26.59 80.8,26.37 80.58,26.16V30.82H73.4V26.27ZM82.51,25.31V22.63H79.14C80.44,23.55 81.57,24.45 82.51,25.31ZM78.87,22.63H75L76.21,23.71C75.51,24.54 74.71,25.29 73.81,25.96H80.38C79.65,25.24 78.76,24.5 77.72,23.75L78.87,22.63ZM74.8,22.63H71.46V25.2C72.79,24.47 73.9,23.6 74.8,22.63ZM78.76,29.16V27.62H75.22V29.16H78.76ZM75.27,20.97H78.44C78.73,20.42 78.98,19.84 79.23,19.26H74.62C74.84,19.8 75.06,20.36 75.27,20.97ZM86.72,20.67H88.12V16.31H89.96V20.67H91.35V22.43H89.96V27.15C90.39,26.93 90.79,26.7 91.18,26.45V28.26C89.85,29.06 88.45,29.72 86.95,30.28L86.49,28.48C87.03,28.34 87.58,28.17 88.12,27.98V22.43H86.72V20.67ZM93.79,24.16C93.76,25.71 93.58,27.11 93.25,28.37H94.39C94.71,28.34 94.91,28.07 95,27.6C95.07,27.11 95.13,25.96 95.14,24.16H93.79ZM93.2,28.55C92.77,30.14 92.14,31.52 91.31,32.69L89.94,31.47C91.26,29.67 91.94,27.15 92.01,23.94V18.78H97.2C97.16,17.97 97.16,17.12 97.16,16.24H98.89L98.92,18.78H100.87C100.51,17.99 100.11,17.28 99.68,16.69L101.05,16.13C101.5,16.78 101.89,17.48 102.22,18.24L100.98,18.78H102.79V20.49H99C99.1,22.61 99.27,24.23 99.48,25.38C99.5,25.51 99.52,25.64 99.55,25.74C100.06,24.57 100.47,23.28 100.78,21.87L102.29,22.5C101.75,24.72 101.03,26.59 100.15,28.14C100.29,28.59 100.44,28.97 100.6,29.25C100.99,30.05 101.26,30.44 101.44,30.44C101.59,30.44 101.73,29.67 101.89,28.12L103.44,28.98C103.06,31.47 102.51,32.71 101.77,32.71C101.12,32.71 100.44,32.17 99.73,31.09C99.48,30.71 99.25,30.28 99.03,29.79C98.06,31.02 96.96,31.95 95.74,32.64L94.62,31.18C96.12,30.42 97.36,29.31 98.37,27.87C98.2,27.31 98.06,26.72 97.92,26.07C97.59,24.59 97.38,22.74 97.27,20.49H93.81V22.59H96.82C96.82,25.98 96.66,28.03 96.37,28.77C96.08,29.49 95.58,29.87 94.82,29.9H93.61L93.2,28.55Z"
            tools:ignore="VectorPath" />
        <path android:pathData="M130.56,19.59C132.02,18.24 134.08,17.77 135.97,18.35L166.02,27.64C167.92,28.22 169.35,29.77 169.79,31.7L176.78,62.37C177.22,64.3 176.6,66.32 175.14,67.67L152.08,89.06C150.62,90.4 148.56,90.87 146.67,90.29L116.62,81C114.72,80.42 113.29,78.87 112.85,76.94L105.86,46.27C105.42,44.34 106.04,42.32 107.5,40.97L130.56,19.59Z">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:endX="149.73"
                    android:endY="91.23"
                    android:startX="132.91"
                    android:startY="17.41"
                    android:type="linear">
                    <item
                        android:color="#FF76C7FF"
                        android:offset="0" />
                    <item
                        android:color="#FF3AADFE"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path android:pathData="M133.51,32.5C134.96,31.16 137.02,30.69 138.91,31.27L156.31,36.65C158.2,37.23 159.64,38.78 160.08,40.71L164.12,58.46C164.56,60.39 163.94,62.41 162.48,63.76L149.13,76.14C147.68,77.48 145.62,77.95 143.73,77.37L126.33,72C124.44,71.41 123,69.86 122.56,67.93L118.52,50.18C118.08,48.25 118.7,46.23 120.16,44.88L133.51,32.5Z">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:endX="146.79"
                    android:endY="78.31"
                    android:startX="135.85"
                    android:startY="30.33"
                    android:type="linear">
                    <item
                        android:color="#FFBBE3FF"
                        android:offset="0" />
                    <item
                        android:color="#FF3AADFE"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
        <path android:pathData="M144.7,64.66C144.01,64.53 143.31,64.69 142.76,65.1L138.02,68.64C136.5,69.77 134.34,68.83 134.1,66.95L133.37,61.06C133.28,60.37 132.91,59.74 132.35,59.34L127.53,55.9C125.99,54.8 126.2,52.44 127.91,51.64L133.26,49.13C133.88,48.84 134.36,48.29 134.56,47.63L136.32,41.97C136.88,40.16 139.18,39.63 140.47,41.02L144.51,45.36C144.99,45.87 145.65,46.16 146.34,46.15L152.25,46.09C154.14,46.08 155.35,48.11 154.43,49.77L151.58,54.96C151.25,55.57 151.18,56.29 151.4,56.95L153.3,62.58C153.9,64.37 152.35,66.16 150.5,65.79L144.7,64.66Z">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:endX="144.31"
                    android:endY="67.42"
                    android:startX="138.13"
                    android:startY="40.3"
                    android:type="linear">
                    <item
                        android:color="#FFBBE3FF"
                        android:offset="0" />
                    <item
                        android:color="#1EBBE3FF"
                        android:offset="1" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
</vector>
