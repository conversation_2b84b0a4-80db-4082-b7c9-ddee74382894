<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_609"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_top24r_sbw_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivRepeatMode"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginVertical="@dimen/base_sw_dp_14"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:background="@drawable/svg_single_loop"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvRepeatText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_4"
        android:text="@string/str_single_loop"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintBottom_toBottomOf="@+id/ivRepeatMode"
        app:layout_constraintStart_toEndOf="@+id/ivRepeatMode"
        app:layout_constraintTop_toTopOf="@+id/ivRepeatMode" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="@dimen/base_sw_dp_44"
        android:layout_height="@dimen/base_sw_dp_44"
        android:background="@drawable/svg_dialog_small_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vLineOne"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_1"
        android:layout_marginTop="@dimen/base_sw_dp_4"
        android:background="@color/border_divider"
        app:layout_constraintTop_toBottomOf="@+id/ivClose" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivAlbumPic"
        android:layout_width="@dimen/base_sw_dp_60"
        android:layout_height="@dimen/base_sw_dp_60"
        android:layout_marginVertical="@dimen/base_sw_dp_12"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vLineOne"
        app:shapeAppearance="@style/Rounded8Style" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAlbumTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_7"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvDetails"
        app:layout_constraintStart_toEndOf="@+id/sivAlbumPic"
        app:layout_constraintTop_toTopOf="@+id/sivAlbumPic"
        tools:text="小猪佩奇 第一季小猪佩奇第一季小猪佩奇" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAlbumDesc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_7"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintEnd_toStartOf="@+id/tvDetails"
        app:layout_constraintStart_toEndOf="@+id/sivAlbumPic"
        app:layout_constraintTop_toBottomOf="@+id/tvAlbumTitle"
        tools:text="小猪佩奇 第一季小猪佩奇第一季小猪佩奇" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetails"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_7"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/base_sw_dp_16"
        android:text="@string/str_details"
        android:textColor="@color/text_link"
        android:textSize="@dimen/text_body_small"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/sivAlbumPic"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/sivAlbumPic" />

    <View
        android:id="@+id/vLineTwo"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:background="@color/border_divider"
        app:layout_constraintTop_toBottomOf="@+id/sivAlbumPic" />

    <com.snails.module.audio.widget.AlbumListBottomView
        android:id="@+id/albumListBottomView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/base_sw_dp_16"
        android:paddingBottom="@dimen/base_sw_dp_20"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toBottomOf="@+id/vLineTwo"
        app:layout_constraintVertical_weight="1"
        tools:listitem="@layout/layout_album_list_bottom_item" />

</androidx.constraintlayout.widget.ConstraintLayout>
