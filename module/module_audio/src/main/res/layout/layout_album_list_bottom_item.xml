<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_54">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStoryName"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/base_sw_dp_4"
        android:ellipsize="marquee"
        android:focusableInTouchMode="true"
        android:gravity="center_vertical"
        android:marqueeRepeatLimit="marquee_forever"
        android:focusable="true"
        android:scrollHorizontally="true"
        android:singleLine="true"
        android:textColor="@color/text_link"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintEnd_toStartOf="@+id/lottieStoryPlay"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="小猪佩奇 01：小猪的奇遇" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottieStoryPlay"
        android:layout_width="@dimen/base_sw_dp_26"
        android:layout_height="@dimen/base_sw_dp_26"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvStoryName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvStoryName"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/lottie_item_play"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>
