<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_sw_dp_406"
    android:layout_gravity="bottom"
    android:background="@drawable/shape_top24r_sbw_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="@dimen/base_sw_dp_44"
        android:layout_height="@dimen/base_sw_dp_44"
        android:layout_marginTop="@dimen/base_sw_dp_4"
        android:background="@drawable/svg_dialog_small_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/vLineOne"
        android:layout_width="match_parent"
        android:layout_height="@dimen/base_sw_dp_1"
        android:layout_marginTop="@dimen/base_sw_dp_4"
        android:background="@color/border_divider"
        app:layout_constraintTop_toBottomOf="@+id/ivClose" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTimerTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:text="@string/str_timer_close"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/headline_h4"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/cbTimerState"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/cbTimerState" />

    <com.google.android.material.checkbox.MaterialCheckBox
        android:id="@+id/cbTimerState"
        android:layout_width="@dimen/base_sw_dp_54"
        android:layout_height="@dimen/base_sw_dp_32"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:background="@drawable/shape_check_state_bg"
        android:button="@null"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/vLineOne" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTimeTxt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_24"
        android:text="@string/str_time"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cbTimerState" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTimeOne"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:gravity="center"
        android:text="@string/str_15_minutes"
        android:textSize="@dimen/base_sw_sp_16"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvTimeTwo"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTimeTxt"
        tools:background="@drawable/shape_timer_select_bg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTimeTwo"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:gravity="center"
        android:text="@string/str_30_minutes"
        android:textSize="@dimen/base_sw_sp_16"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvTimeThree"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvTimeOne"
        app:layout_constraintTop_toBottomOf="@+id/tvTimeTxt"
        tools:background="@drawable/shape_timer_select_bg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTimeThree"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:gravity="center"
        android:text="@string/str_60_minutes"
        android:textSize="@dimen/base_sw_sp_16"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvTimeTwo"
        app:layout_constraintTop_toBottomOf="@+id/tvTimeTxt"
        tools:background="@drawable/shape_timer_select_bg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCountTxt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_38"
        android:text="@string/str_count"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_medium"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTimeOne" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCountOne"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginStart="@dimen/base_sw_dp_16"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:gravity="center"
        android:text="@string/str_1_count"
        android:textSize="@dimen/base_sw_sp_16"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvTimeTwo"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvCountTxt"
        tools:background="@drawable/shape_timer_select_bg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCountTwo"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginHorizontal="@dimen/base_sw_dp_12"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:gravity="center"
        android:text="@string/str_3_count"
        android:textSize="@dimen/base_sw_sp_16"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/tvCountThree"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvCountOne"
        app:layout_constraintTop_toBottomOf="@+id/tvCountTxt"
        tools:background="@drawable/shape_timer_select_bg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCountThree"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_48"
        android:layout_marginTop="@dimen/base_sw_dp_12"
        android:layout_marginEnd="@dimen/base_sw_dp_16"
        android:gravity="center"
        android:text="@string/str_5_count"
        android:textSize="@dimen/base_sw_sp_16"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@+id/tvCountTwo"
        app:layout_constraintTop_toBottomOf="@+id/tvCountTxt"
        tools:background="@drawable/shape_timer_select_bg" />

</androidx.constraintlayout.widget.ConstraintLayout>
