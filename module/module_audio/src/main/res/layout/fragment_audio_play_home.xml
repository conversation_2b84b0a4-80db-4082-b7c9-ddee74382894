<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/svg_play_bg">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_44"
        app:commBackIcon="@drawable/svg_arrow_down_bold"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivAudioBg"
        android:layout_width="@dimen/base_sw_dp_244"
        android:layout_height="@dimen/base_sw_dp_252"
        android:layout_marginTop="@dimen/base_sw_dp_32"
        android:background="@drawable/svg_audio_play_album_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/commonTitleView" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/leverLottie"
        android:layout_width="@dimen/base_sw_dp_57"
        android:layout_height="@dimen/base_sw_dp_165"
        app:layout_constraintEnd_toEndOf="@+id/ivAudioBg"
        app:layout_constraintTop_toTopOf="@+id/ivAudioBg"
        app:lottie_autoPlay="false"
        app:lottie_loop="false"
        app:lottie_rawRes="@raw/lottie_lever_up" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivAudioPic"
        android:layout_width="@dimen/base_sw_dp_136"
        android:layout_height="@dimen/base_sw_dp_136"
        android:layout_marginTop="@dimen/base_sw_dp_54"
        android:background="#00f"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="@+id/ivAudioBg"
        app:layout_constraintStart_toStartOf="@+id/ivAudioBg"
        app:layout_constraintTop_toTopOf="@+id/ivAudioBg"
        app:shapeAppearance="@style/CircleStyle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStoryName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/base_sw_dp_66"
        android:layout_marginTop="@dimen/base_sw_dp_44"
        android:ellipsize="marquee"
        android:focusable="true"
        android:gravity="center"
        android:marqueeRepeatLimit="marquee_forever"
        android:scrollHorizontally="true"
        android:singleLine="true"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/text_body_large"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivAudioBg" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTime"
        android:layout_width="@dimen/base_sw_dp_40"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_24"
        android:gravity="center"
        android:text="00:00"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/button_h2"
        app:layout_constraintBottom_toBottomOf="@+id/seekBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/seekBar" />

    <androidx.appcompat.widget.AppCompatSeekBar
        android:id="@+id/seekBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="center_vertical"
        android:layout_marginHorizontal="@dimen/base_sw_dp_6"
        android:layout_marginTop="@dimen/base_sw_dp_44"
        android:background="@null"
        android:max="100"
        android:maxHeight="@dimen/base_sw_dp_10"
        android:minHeight="@dimen/base_sw_dp_10"
        android:progressDrawable="@drawable/shape_audio_play_seek_progress"
        android:splitTrack="false"
        android:thumb="@drawable/ic_audio_seek_thumb"
        app:layout_constraintEnd_toStartOf="@+id/tvTotalTime"
        app:layout_constraintStart_toEndOf="@+id/tvTime"
        app:layout_constraintTop_toBottomOf="@+id/tvStoryName" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTotalTime"
        android:layout_width="@dimen/base_sw_dp_40"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/base_sw_dp_24"
        android:gravity="center"
        android:text="00:00"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/button_h2"
        app:layout_constraintBottom_toBottomOf="@+id/seekBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/seekBar" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPlayOrPause"
        android:layout_width="@dimen/base_sw_dp_72"
        android:layout_height="@dimen/base_sw_dp_72"
        android:layout_marginTop="@dimen/base_sw_dp_28"
        android:background="@drawable/ic_audio_pause"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/seekBar" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lottieLoading"
        android:layout_width="@dimen/base_sw_dp_72"
        android:layout_height="@dimen/base_sw_dp_72"
        android:layout_marginTop="@dimen/base_sw_dp_28"
        android:background="@drawable/ic_lottie_bg"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/seekBar"
        app:lottie_autoPlay="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/lottie_play_loading"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPlayPrevious"
        android:layout_width="@dimen/base_sw_dp_40"
        android:layout_height="@dimen/base_sw_dp_40"
        android:layout_marginEnd="@dimen/base_sw_dp_28"
        android:background="@drawable/svg_audio_previous"
        app:layout_constraintBottom_toBottomOf="@+id/ivPlayOrPause"
        app:layout_constraintEnd_toStartOf="@+id/ivPlayOrPause"
        app:layout_constraintTop_toTopOf="@+id/ivPlayOrPause" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPlayNext"
        android:layout_width="@dimen/base_sw_dp_40"
        android:layout_height="@dimen/base_sw_dp_40"
        android:layout_marginStart="@dimen/base_sw_dp_28"
        android:background="@drawable/svg_audio_next"
        app:layout_constraintBottom_toBottomOf="@+id/ivPlayOrPause"
        app:layout_constraintStart_toEndOf="@+id/ivPlayOrPause"
        app:layout_constraintTop_toTopOf="@+id/ivPlayOrPause" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPlayLoopMode"
        android:layout_width="@dimen/base_sw_dp_32"
        android:layout_height="@dimen/base_sw_dp_32"
        android:layout_marginEnd="@dimen/base_sw_dp_28"
        android:background="@drawable/svg_single_loop"
        app:layout_constraintBottom_toBottomOf="@+id/ivPlayOrPause"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/ivPlayPrevious"
        app:layout_constraintTop_toTopOf="@+id/ivPlayOrPause" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivPlayTimer"
        android:layout_width="@dimen/base_sw_dp_32"
        android:layout_height="@dimen/base_sw_dp_32"
        android:layout_marginStart="@dimen/base_sw_dp_28"
        android:background="@drawable/svg_audio_timer_gray"
        app:layout_constraintBottom_toBottomOf="@+id/ivPlayOrPause"
        app:layout_constraintStart_toEndOf="@+id/ivPlayNext"
        app:layout_constraintTop_toTopOf="@+id/ivPlayOrPause" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTimeDown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_5"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/text_body_small"
        app:layout_constraintEnd_toEndOf="@+id/ivPlayTimer"
        app:layout_constraintStart_toStartOf="@+id/ivPlayTimer"
        app:layout_constraintTop_toBottomOf="@+id/ivPlayTimer"
        tools:text="00:00" />

    <View
        android:id="@+id/vAlbumBg"
        android:layout_width="0dp"
        android:layout_height="@dimen/base_sw_dp_84"
        android:layout_marginHorizontal="@dimen/base_sw_dp_24"
        android:layout_marginTop="@dimen/base_sw_dp_44"
        android:background="@drawable/shape_album_list_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivPlayOrPause" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/sivAlbumPic"
        android:layout_width="@dimen/base_sw_dp_60"
        android:layout_height="@dimen/base_sw_dp_60"
        android:layout_marginStart="@dimen/base_sw_dp_12"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="@+id/vAlbumBg"
        app:layout_constraintStart_toStartOf="@+id/vAlbumBg"
        app:layout_constraintTop_toTopOf="@+id/vAlbumBg"
        app:shapeAppearance="@style/Rounded8Style" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAlbumTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginTop="@dimen/base_sw_dp_6"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/text_headline"
        android:textSize="@dimen/button_h1"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@+id/ivAlbumList"
        app:layout_constraintStart_toEndOf="@+id/sivAlbumPic"
        app:layout_constraintTop_toTopOf="@+id/sivAlbumPic"
        tools:text="小猪佩奇 第一季小猪佩奇小猪佩奇" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAlbumCount"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/base_sw_dp_8"
        android:layout_marginEnd="@dimen/base_sw_dp_12"
        android:layout_marginBottom="@dimen/base_sw_dp_6"
        android:textColor="@color/text_describe"
        android:textSize="@dimen/button_h2"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@+id/sivAlbumPic"
        app:layout_constraintEnd_toStartOf="@+id/ivAlbumList"
        app:layout_constraintStart_toEndOf="@+id/sivAlbumPic"
        tools:text="共73集" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivAlbumList"
        android:layout_width="@dimen/base_sw_dp_24"
        android:layout_height="@dimen/base_sw_dp_24"
        android:layout_marginEnd="@dimen/base_sw_dp_20"
        android:background="@drawable/svg_album_list"
        app:layout_constraintBottom_toBottomOf="@+id/vAlbumBg"
        app:layout_constraintEnd_toEndOf="@+id/vAlbumBg"
        app:layout_constraintTop_toTopOf="@+id/vAlbumBg" />

</androidx.constraintlayout.widget.ConstraintLayout>