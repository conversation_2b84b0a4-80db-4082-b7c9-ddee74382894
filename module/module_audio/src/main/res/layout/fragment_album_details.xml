<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/surface_primary_button"
    android:orientation="vertical"
    android:scrollbars="none">

    <com.snails.module.base.widget.CommonTitleView
        android:id="@+id/commonTitleView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/base_sw_dp_44"
        app:commBackIcon="@drawable/svg_common_white_back"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:scrollbars="none">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">

            <View
                android:id="@+id/vAlbumPicBg"
                android:layout_width="@dimen/base_sw_dp_88"
                android:layout_height="@dimen/base_sw_dp_88"
                android:layout_marginStart="@dimen/base_sw_dp_16"
                android:layout_marginTop="@dimen/base_sw_dp_8"
                android:background="@drawable/shape_sbw_13r_bg"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/sivAlbumPic"
                android:layout_width="@dimen/base_sw_dp_84"
                android:layout_height="@dimen/base_sw_dp_84"
                app:layout_constraintBottom_toBottomOf="@+id/vAlbumPicBg"
                app:layout_constraintEnd_toEndOf="@+id/vAlbumPicBg"
                app:layout_constraintStart_toStartOf="@+id/vAlbumPicBg"
                app:layout_constraintTop_toTopOf="@+id/vAlbumPicBg"
                app:shapeAppearance="@style/Rounded12Style" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvAlbumTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/base_sw_dp_12"
                android:textColor="@color/text_on_primary_button"
                android:textSize="@dimen/headline_h4"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@+id/vAlbumPicBg"
                app:layout_constraintTop_toTopOf="@+id/vAlbumPicBg"
                tools:text="小猪佩奇 第一季" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvLabelOne"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/base_sw_dp_12"
                android:layout_marginTop="@dimen/base_sw_dp_8"
                android:background="@drawable/shape_label_bg"
                android:maxLength="4"
                android:textColor="@color/text_link"
                android:textSize="@dimen/text_body_footnote"
                android:visibility="gone"
                app:layout_constraintStart_toEndOf="@+id/vAlbumPicBg"
                app:layout_constraintTop_toBottomOf="@+id/tvAlbumTitle"
                tools:text="72集"
                tools:visibility="visible" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvLabelTwo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/base_sw_dp_8"
                android:layout_marginTop="@dimen/base_sw_dp_8"
                android:background="@drawable/shape_label_bg"
                android:maxLength="4"
                android:textColor="@color/text_link"
                android:textSize="@dimen/text_body_footnote"
                android:visibility="gone"
                app:layout_constraintStart_toEndOf="@+id/tvLabelOne"
                app:layout_constraintTop_toBottomOf="@+id/tvAlbumTitle"
                tools:text="72集"
                tools:visibility="visible" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvLabelThree"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/base_sw_dp_8"
                android:layout_marginTop="@dimen/base_sw_dp_8"
                android:background="@drawable/shape_label_bg"
                android:maxLength="4"
                android:textColor="@color/text_link"
                android:textSize="@dimen/text_body_footnote"
                android:visibility="gone"
                app:layout_constraintStart_toEndOf="@+id/tvLabelTwo"
                app:layout_constraintTop_toBottomOf="@+id/tvAlbumTitle"
                tools:text="72集"
                tools:visibility="visible" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvLabelFour"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/base_sw_dp_8"
                android:layout_marginTop="@dimen/base_sw_dp_8"
                android:background="@drawable/shape_label_bg"
                android:maxLength="4"
                android:textColor="@color/text_link"
                android:textSize="@dimen/text_body_footnote"
                android:visibility="gone"
                app:layout_constraintStart_toEndOf="@+id/tvLabelThree"
                app:layout_constraintTop_toBottomOf="@+id/tvAlbumTitle"
                tools:text="72集"
                tools:visibility="visible" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvAlbumDesc"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/base_sw_dp_16"
                android:layout_marginTop="@dimen/base_sw_dp_12"
                android:ellipsize="end"
                android:maxLines="3"
                android:textColor="@color/text_on_primary_button"
                android:textSize="@dimen/text_body_small"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/vAlbumPicBg"
                tools:text="粉红猪小妹佩奇是一只非常可爱的小粉红猪，她与弟弟乔治、爸爸、妈妈快乐地住在一起。粉红猪小妹最喜欢做的事情是玩游戏，打扮的漂漂亮亮，度假，以及住在小泥坑里快乐地跳上跳下！除了这些，她还喜欢到处探险，虽然有些时候会遇到一些小状况，但总可以化险为夷，而且都会带给大家意外的惊喜！" />

            <FrameLayout
                android:id="@+id/flyContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/base_sw_dp_16"
                android:background="@drawable/shape_top24r_sbw_bg"
                android:paddingHorizontal="@dimen/base_sw_dp_16"
                android:paddingTop="@dimen/base_sw_dp_20"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvAlbumDesc"
                tools:ignore="WebViewLayout">

                <WebView
                    android:id="@+id/scrollableWebView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:scrollbars="none" />
            </FrameLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</LinearLayout>