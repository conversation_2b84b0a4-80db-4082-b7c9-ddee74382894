<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_navigation"
    app:startDestination="@id/audioPlayHomeFragment">

    <fragment
        android:id="@+id/audioPlayHomeFragment"
        android:name="com.snails.module.audio.ui.AudioPlayHomeFragment"
        android:label="AudioPlayHomeFragment"
        tools:layout="@layout/fragment_audio_play_home" >
        <action
            android:id="@+id/action_audioPlayHomeFragment_to_albumDetailsFragment"
            app:destination="@id/albumDetailsFragment" />
    </fragment>

    <fragment
        android:id="@+id/albumDetailsFragment"
        android:name="com.snails.module.audio.ui.AlbumDetailsFragment"
        android:label="AlbumDetailsFragment"
        tools:layout="@layout/fragment_album_details" />

</navigation>