package com.snails.module.audio.dialog

import com.blankj.utilcode.util.StringUtils
import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.expand.AlbumListInfo
import com.snails.base.network.repository.info.expand.AlbumItemInfo
import com.snails.base.utils.ext.singleClick
import com.snails.module.audio.R
import com.snails.module.audio.databinding.DialogAlbumListBottomBinding
import com.snails.module.audio.widget.AlbumListBottomView.ItemClickListener

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月12日 11:03:33
 */
class AlbumListBottomDialog : BaseBottomDialog<DialogAlbumListBottomBinding>(heightDimen = 609) {
    private var currentListInfo: AlbumListInfo? = null //当前专辑列表数据
    private var currentItemInfo: AlbumItemInfo? = null //当前正在播放的专辑资源
    var loopMode: Int = 0
    var listener: ClickListener? = null
    override fun initView() {
        super.initView()
        setData(currentListInfo)
    }

    fun setAlbumListInfo(data: AlbumListInfo) {
        this.currentListInfo = data
    }

    fun setCurrentPlayItem(data: AlbumItemInfo?) {
        this.currentItemInfo = data
    }

    override fun initClick() {
        super.initClick()
        binding.tvDetails.singleClick {
            currentListInfo?.albumId?.let { it1 -> listener?.clickDetails(it1) }
            dismiss()
        }
        binding.ivClose.singleClick {
            dismiss()
        }
        binding.albumListBottomView.listener = object : ItemClickListener {
            override fun itemClick(data: AlbumItemInfo, pos: Int) {
                listener?.clickItem(data, pos)
                dismiss()
            }
        }
    }

    private fun setData(info: AlbumListInfo?) {
        info ?: return
        binding.apply {
            info.albumCover?.let {
                sivAlbumPic.load(
                    it,
                    error = R.drawable.svg_w88_h88_error,
                    placeholder = R.drawable.svg_w88_h88_placeholder
                )
            }
            tvAlbumTitle.text = info.albumName ?: ""
            tvAlbumDesc.text = info.description ?: ""
            info.items?.let {
                if (currentItemInfo != null) {
                    it.forEach { info ->
                        info.isPlaying = (currentItemInfo?.itemId == info.itemId)
                    }
                }
                albumListBottomView.setData(it)
            }
        }
        showLoopMode(loopMode)
    }

    private fun showLoopMode(loopMode: Int) {
        if (loopMode == 1) {
            binding.ivRepeatMode.setBackgroundResource(R.drawable.svg_circle_loop)
            binding.tvRepeatText.text = StringUtils.getString(R.string.str_circle_loop)
        } else {
            binding.ivRepeatMode.setBackgroundResource(R.drawable.svg_single_loop)
            binding.tvRepeatText.text = StringUtils.getString(R.string.str_single_loop)
        }
    }

    fun updatePlayItem(bean: AlbumItemInfo?) {
        val data = bean ?: return
        currentListInfo?.items?.apply {
            forEach { info ->
                info.isPlaying = (data.itemId == info.itemId)
            }
            binding.albumListBottomView.setData(this)
        }
    }

    interface ClickListener {
        fun clickDetails(albumId: String)
        fun clickItem(data: AlbumItemInfo, pos: Int)
    }
}