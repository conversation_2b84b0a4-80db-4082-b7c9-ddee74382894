package com.snails.module.audio.dialog

import android.content.DialogInterface
import android.graphics.Typeface
import androidx.appcompat.widget.AppCompatTextView
import com.blankj.utilcode.util.ColorUtils
import com.snails.base.audio.bean.PlayTimerType
import com.snails.base.dialog.BaseBottomDialog
import com.snails.base.utils.ext.singleClick
import com.snails.module.audio.R
import com.snails.module.audio.databinding.DialogTimerBottomBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月12日 11:03:33
 */
class TimerBottomDialog() : BaseBottomDialog<DialogTimerBottomBinding>(heightDimen = 406) {

    var listener: ClickListener? = null
    private var currentPlayTimerType: PlayTimerType? = null
    private var chooserView: AppCompatTextView? = null
    private var type: PlayTimerType? = null
    private val list = mutableListOf<AppCompatTextView>()

    constructor(type: PlayTimerType?) : this() {
        this.type = type
    }

    override fun initView() {
        super.initView()
        currentPlayTimerType = type
        list.add(binding.tvTimeOne)
        list.add(binding.tvTimeTwo)
        list.add(binding.tvTimeThree)
        list.add(binding.tvCountOne)
        list.add(binding.tvCountTwo)
        list.add(binding.tvCountThree)
        showTimerState()
    }

    override fun initClick() {
        list.forEachIndexed { index, view ->
            view.singleClick {
                if (chooserView == null || chooserView != view) {
                    val type = getType(index)
                    currentPlayTimerType = type
                    listener?.chooseType(type)
                    chooserView = view
                } else {
                    currentPlayTimerType = null
                    chooserView = null
                    listener?.closeTimer()
                }
                showTimerState()
            }
        }
        binding.ivClose.singleClick {
            dismiss()
        }
        binding.cbTimerState.setOnCheckedChangeListener { _, isChecked ->
            if (!isChecked) {
                currentPlayTimerType = null
            }
            updateCbTimerState(isChecked)
            showTimerState()
        }
    }

    private fun updateCbTimerState(isChecked: Boolean) {
        if (currentPlayTimerType != null) {
            return
        }
        if (isChecked) {
            val type = getType(1)
            currentPlayTimerType = type
            listener?.chooseType(type)
            chooserView = binding.tvTimeTwo
            return
        }
        currentPlayTimerType = null
        chooserView = null
        listener?.closeTimer()
    }

    private fun showTimerState() {
        val view = getView(currentPlayTimerType)
        var select = false
        list.forEach { v ->
            if (view == v) {
                select = true
                v.setBackgroundResource(R.drawable.shape_timer_select_bg)
                v.setTextColor(ColorUtils.getColor(R.color.text_on_primary_button))
                v.setTypeface(null, Typeface.BOLD)
            } else {
                v.setBackgroundResource(R.drawable.shape_timer_unselect_bg)
                v.setTextColor(ColorUtils.getColor(R.color.text_body))
                v.setTypeface(null, Typeface.NORMAL)
            }
        }
        binding.cbTimerState.isChecked = select
        if (currentPlayTimerType == null) {
            binding.cbTimerState.setBackgroundResource(R.drawable.svg_off)
        } else {
            binding.cbTimerState.setBackgroundResource(R.drawable.svg_on)
        }
    }

    interface ClickListener {
        fun chooseType(type: PlayTimerType?)
        fun closeTimer()
    }

    private fun getType(pos: Int): PlayTimerType? {
        return when (pos) {
            0 -> {
                PlayTimerType.MINUTES_15
            }

            1 -> {
                PlayTimerType.MINUTES_30
            }

            2 -> {
                PlayTimerType.MINUTES_60
            }

            3 -> {
                PlayTimerType.COUNT_1
            }

            4 -> {
                PlayTimerType.COUNT_3
            }

            5 -> {
                PlayTimerType.COUNT_5
            }

            else -> {
                null
            }
        }
    }

    private fun getView(type: PlayTimerType?): AppCompatTextView? {
        return when (type) {
            PlayTimerType.MINUTES_15 -> {
                binding.tvTimeOne
            }

            PlayTimerType.MINUTES_30 -> {
                binding.tvTimeTwo
            }

            PlayTimerType.MINUTES_60 -> {
                binding.tvTimeThree
            }

            PlayTimerType.COUNT_1 -> {
                binding.tvCountOne
            }

            PlayTimerType.COUNT_3 -> {
                binding.tvCountTwo
            }

            PlayTimerType.COUNT_5 -> {
                binding.tvCountThree
            }

            else -> {
                null
            }
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        listener = null
        super.onDismiss(dialog)
    }

    override fun dismiss() {
        listener = null
        super.dismiss()
    }
}