package com.snails.module.audio.ui

import android.annotation.SuppressLint
import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import com.blankj.utilcode.util.ColorUtils
import com.gyf.immersionbar.ImmersionBar
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.expand.AlbumDetailsInfo
import com.snails.base.utils.ext.visible
import com.snails.module.audio.R
import com.snails.module.audio.databinding.FragmentAlbumDetailsBinding
import com.snails.module.audio.viewmodel.AudioViewModel
import com.snails.module.base.BaseStateFragment

/**
 * @Description 专辑详情页
 * <AUTHOR>
 * @CreateTime 2024年11月12日 13:02:48
 */
class AlbumDetailsFragment : BaseStateFragment<FragmentAlbumDetailsBinding>() {

    private var titleHeightMax: Int? = null

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(false).init()
    }

    private val audioViewModel: AudioViewModel by viewModels()

    override fun createViewModel() = audioViewModel

    override fun initData() {
        super.initData()
        titleHeightMax = context?.resources?.getDimensionPixelSize(R.dimen.base_sw_dp_72)
        audioViewModel.albumId = activity?.intent?.getStringExtra("id")
        audioViewModel.getAudioAlbumDetails()
    }

    override fun initClick() {
        super.initClick()
        binding.apply {
            commonTitleView.setBackClickListener {
                val pop = Navigation.findNavController(it).popBackStack()
                if (!pop) {
                    requireActivity().finish()
                }
            }
        }
    }

    override fun initObserve() {
        super.initObserve()
        audioViewModel.albumDetailsInfoLiveData.observe(viewLifecycleOwner) {
            initUi(it)
        }
    }

    override fun initView() {
        super.initView()
        binding.scrollView.setOnScrollChangeListener { _, _, scrollY, _, _ ->
            animationTitle(scrollY)
        }
    }

    private fun animationTitle(verticalOffset: Int) {
        val heightMax = titleHeightMax ?: return
        val currentScrollDistance = if (verticalOffset > heightMax) {
            heightMax
        } else {
            verticalOffset
        }
        val p = currentScrollDistance.toFloat() / heightMax
        binding.commonTitleView.getTitleView().alpha = p
    }


    private fun initUi(info: AlbumDetailsInfo) {
        binding.apply {
            tvAlbumTitle.text = info.albumName ?: ""
            info.albumCover?.let {
                sivAlbumPic.load(
                    it,
                    error = R.drawable.svg_w88_h88_error,
                    placeholder = R.drawable.svg_w88_h88_placeholder
                )
            }
            tvAlbumDesc.text = info.description ?: ""
            showLabel(info.tags)
            showWeb(info.detail)
            commonTitleView.apply {
                setTitle(info.albumName ?: "")
                getTitleView().apply {
                    setTextColor(ColorUtils.getColor(R.color.text_on_primary_button))
                    alpha = 0f
                }
            }
        }
    }

    private fun showLabel(tags: List<String>?) {
        tags?.forEachIndexed { index, s ->
            when (index) {
                0 -> {
                    binding.tvLabelOne.apply {
                        visible()
                        text = s
                    }
                }

                1 -> {
                    binding.tvLabelTwo.apply {
                        visible()
                        text = s
                    }
                }

                2 -> {
                    binding.tvLabelThree.apply {
                        visible()
                        text = s
                    }
                }

                3 -> {
                    binding.tvLabelFour.apply {
                        visible()
                        text = s
                    }
                }
            }
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun showWeb(content: String?) {
        val h5 = content ?: return
        binding.scrollableWebView.apply {
            loadDataWithBaseURL(null, h5, "text/html", "UTF-8", null)
        }
    }

    override fun onRetry() {
        super.onRetry()
        audioViewModel.getAudioAlbumDetails()
    }
}