package com.snails.module.audio.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.snails.base.multi_type.MultiTypeAdapter
import com.snails.base.network.repository.info.expand.AlbumItemInfo
import com.snails.common.widget.MyDividerItemDecoration
import com.snails.module.audio.R
import com.snails.module.audio.viewbinder.AlbumListBottomViewBinder

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月12日 11:26:03
 */
class AlbumListBottomView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RecyclerView(context, attrs, defStyleAttr) {

    private val listAdapter = MultiTypeAdapter()
    var listener: ItemClickListener? = null

    init {
        initView()
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private fun initView() {
        // 列表项
        listAdapter.apply {
            register(AlbumItemInfo::class.java, AlbumListBottomViewBinder { info, pos ->
                listener?.itemClick(info, pos)
            })
        }
        adapter = listAdapter
        addItemDecoration(MyDividerItemDecoration(context, VERTICAL).apply {
            setDrawable(context.resources.getDrawable(R.drawable.divider_review_list_item, null))
        })
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setData(tabList: List<AlbumItemInfo>) {
        listAdapter.items = tabList
        listAdapter.notifyDataSetChanged()
    }

    inner class ItemDecoration : RecyclerView.ItemDecoration() {
        override fun getItemOffsets(
            outRect: Rect,
            view: View,
            parent: RecyclerView,
            state: State
        ) {
            super.getItemOffsets(outRect, view, parent, state)
            outRect.top = context.resources.getDimensionPixelSize(R.dimen.base_sw_dp_12)
        }
    }

    interface ItemClickListener {
        fun itemClick(data: AlbumItemInfo, pos: Int)
    }
}