package com.snails.module.audio.widget

import android.content.Context
import android.util.AttributeSet
import android.webkit.WebView

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月14日 14:29:43
 */
class ScrollableWebView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : WebView(context, attrs, defStyleAttr) {

    var onScrollChangeListener: ((scrollY: Int) -> Unit)? = null

    override fun onScrollChanged(l: Int, t: Int, oldl: Int, oldt: Int) {
        super.onScrollChanged(l, t, oldl, oldt)
        onScrollChangeListener?.invoke(t)
    }
}