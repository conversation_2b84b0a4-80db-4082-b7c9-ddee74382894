package com.snails.module.picturebook.ui

import androidx.fragment.app.viewModels
import androidx.navigation.Navigation
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ColorUtils
import com.gyf.immersionbar.ImmersionBar
import com.snails.module.base.BaseStateFragment
import com.snails.module.picturebook.R
import com.snails.module.picturebook.bean.AlbumHeadBean
import com.snails.module.picturebook.bean.IPictureBookDetails
import com.snails.module.picturebook.databinding.FragmentPictureBookDetailsBinding
import com.snails.module.picturebook.viewmodel.PictureBookViewModel

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月21日 10:32:29
 */
class PictureBookDetailsFragment : BaseStateFragment<FragmentPictureBookDetailsBinding>() {

    private var titleHeightMax: Int? = null

    private val scrollListener = object : RecyclerView.OnScrollListener() {
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            val verticalOffset: Int = recyclerView.computeVerticalScrollOffset()
            animationTitle(verticalOffset)
        }
    }

    override fun beforeSetContentView() {
        super.beforeSetContentView()
        ImmersionBar.with(this).transparentStatusBar().statusBarDarkFont(false).init()
    }

    private val pictureBookViewModel: PictureBookViewModel by viewModels()

    override fun createViewModel() = pictureBookViewModel

    override fun initData() {
        super.initData()
        titleHeightMax = context?.resources?.getDimensionPixelSize(R.dimen.base_sw_dp_72)
        pictureBookViewModel.albumId = activity?.intent?.getStringExtra("id")
    }

    override fun initObserve() {
        super.initObserve()
        pictureBookViewModel.albumDetailsLiveData.observe(viewLifecycleOwner) {
            binding.pictureBookDetailsListView.setData(it)
            it.firstOrNull()?.let { info ->
                showTitleInfo(info)
            }
        }
    }

    private fun showTitleInfo(info: IPictureBookDetails) {
        if (info is AlbumHeadBean) {
            binding.commonTitleView.apply {
                setTitle(info.albumName ?: "")
                getTitleView().apply {
                    setTextColor(ColorUtils.getColor(R.color.text_on_primary_button))
                    alpha = 0f
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        pictureBookViewModel.getAlbumList()
    }

    override fun initClick() {
        super.initClick()
        binding.commonTitleView.setBackClickListener {
            val pop = Navigation.findNavController(it).popBackStack()
            if (!pop) {
                requireActivity().finish()
            }
        }
    }

    override fun initView() {
        super.initView()
        binding.pictureBookDetailsListView.addOnScrollListener(scrollListener)
    }

    override fun onRetry() {
        super.onRetry()
        pictureBookViewModel.getAlbumList()
    }

    private fun animationTitle(verticalOffset: Int) {
        val heightMax = titleHeightMax ?: return
        val currentScrollDistance = if (verticalOffset > heightMax) {
            heightMax
        } else {
            verticalOffset
        }
        val p = currentScrollDistance.toFloat() / heightMax
        binding.commonTitleView.getTitleView().alpha = p
    }

    override fun onDestroy() {
        binding.pictureBookDetailsListView.removeOnScrollListener(scrollListener)
        super.onDestroy()
    }
}