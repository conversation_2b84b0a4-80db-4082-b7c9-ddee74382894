package com.snails.module.picturebook.viewmodel

import androidx.lifecycle.MutableLiveData
import com.snails.base.network.repository.SnailRepository
import com.snails.base.network.repository.info.expand.AlbumListInfo
import com.snails.module.base.BaseViewModel
import com.snails.module.base.bean.StateType
import com.snails.module.base.utils.SingleLiveEventLiveData
import com.snails.module.picturebook.bean.AlbumHeadBean
import com.snails.module.picturebook.bean.AlbumItemBean
import com.snails.module.picturebook.bean.IPictureBookDetails

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月21日10:35:44
 */
class PictureBookViewModel : BaseViewModel() {

    var albumId: String? = null
    val albumDetailsLiveData: MutableLiveData<List<IPictureBookDetails>> = SingleLiveEventLiveData()

    fun getAlbumList() {
        val aId = albumId ?: return
        request(
            stateType = StateType.PAGE,
            request = {
                SnailRepository.me.getAlbumList(aId)
            },
            success = { data ->
                data?.let {
                    albumDetailsLiveData.value = convertData(it)
                }
            }
        )
    }

    private fun convertData(albumListInfo: AlbumListInfo): List<IPictureBookDetails> {
        val list = mutableListOf<IPictureBookDetails>()
        val albumHeadBean = AlbumHeadBean(
            albumCover = albumListInfo.albumCover,
            albumId = albumListInfo.albumId,
            albumName = albumListInfo.albumName,
            albumSize = albumListInfo.albumSize,
            description = albumListInfo.description,
            detail = albumListInfo.detail,
            tags = albumListInfo.tags,
            currentIndex = albumListInfo.currentIndex,
        )
        list.add(albumHeadBean)
        albumListInfo.items?.forEachIndexed { index, info ->
            val albumItemBean = AlbumItemBean(
                itemCover = info.itemCover,
                itemId = info.itemId,
                itemIndex = info.itemIndex,
                itemName = info.itemName,
                itemResource = info.itemResource,
                itemResourceType = info.itemResourceType,
                resourceLength = info.resourceLength,
                albumId = info.albumId,
                itemRoute = info.itemRoute,
                isHistory = (index == albumListInfo.currentIndex - 1)
            )
            list.add(albumItemBean)
        }
        return list
    }
}