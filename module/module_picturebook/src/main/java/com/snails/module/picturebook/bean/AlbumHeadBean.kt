package com.snails.module.picturebook.bean

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月21日 10:54:21
 */
data class AlbumHeadBean(
    val albumCover: String? = null,
    var albumId: String? = null,
    val albumName: String? = null,
    val albumSize: String? = null,
    val description: String? = null,
    val detail: String? = null,
    val tags: List<String>? = null,
    val currentIndex: Int = 1 //播放记录
) : IPictureBookDetails() {
    override fun isSame(data: IPictureBookDetails): Bo<PERSON>an {
        if (data !is AlbumHeadBean) {
            return false
        }
        if (albumCover != data.albumCover) {
            return false
        }
        if (albumId != data.albumId) {
            return false
        }
        if (albumName != data.albumName) {
            return false
        }
        if (albumSize != data.albumSize) {
            return false
        }
        if (description != data.description) {
            return false
        }
        if (detail != data.detail) {
            return false
        }
        if (currentIndex != data.currentIndex) {
            return false
        }
        if (tags?.size != data.tags?.size) {
            return false
        }
        return true
    }
}
