package com.snails.module.picturebook.bean

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年11月21日 10:54:21
 */
data class AlbumItemBean(
    val itemCover: String? = null,
    val itemId: String? = null,
    val itemIndex: Int? = null,
    val itemName: String? = null,
    val itemResource: String? = null,
    val itemResourceType: String? = null,
    val resourceLength: Int? = null,
    var isHistory: Boolean = false,
    var albumId: String? = null,
    var itemRoute: String? = null,
) : IPictureBookDetails() {
    override fun isSame(data: IPictureBookDetails): Boolean {
        if (data !is AlbumItemBean) {
            return false
        }
        if (itemCover != data.itemCover) {
            return false
        }
        if (itemId != data.itemId) {
            return false
        }
        if (itemIndex != data.itemIndex) {
            return false
        }
        if (itemName != data.itemName) {
            return false
        }
        if (itemResource != data.itemResource) {
            return false
        }
        if (itemResourceType != data.itemResourceType) {
            return false
        }
        if (resourceLength != data.resourceLength) {
            return false
        }
        if (isHistory != data.isHistory) {
            return false
        }
        if (albumId != data.albumId) {
            return false
        }
        if (itemRoute != data.itemRoute) {
            return false
        }
        return true
    }
}
