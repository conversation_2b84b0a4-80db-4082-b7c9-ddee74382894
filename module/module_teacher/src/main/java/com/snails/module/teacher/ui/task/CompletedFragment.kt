package com.snails.module.teacher.ui.task

import androidx.fragment.app.viewModels
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.scwang.smart.refresh.layout.listener.OnRefreshLoadMoreListener
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.BaseStateFragment
import com.snails.module.base.bean.StateType
import com.snails.module.teacher.databinding.FragmentTaskCompletedBinding
import com.snails.module.teacher.viewmodel.TeacherTaskViewModel

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年01月09日 16:40:20
 */
class CompletedFragment : BaseStateFragment<FragmentTaskCompletedBinding>() {

    //是否首次显示
    private var isFirstShow = true

    private val teacherTaskViewModel: TeacherTaskViewModel by viewModels()

    override fun createViewModel() = teacherTaskViewModel

    //模糊查询的学生姓名
    var cacheStudentName: String? = null

    override fun initData() {
        super.initData()
        teacherTaskViewModel.studentName = cacheStudentName
        teacherTaskViewModel.getReviewedList(StateType.PAGE)
    }

    override fun initClick() {
        super.initClick()

        binding.refreshLayout.setOnRefreshLoadMoreListener(object : OnRefreshLoadMoreListener {
            override fun onRefresh(refreshLayout: RefreshLayout) {
                teacherTaskViewModel.reviewedPage = 1
                teacherTaskViewModel.getReviewedList()
            }

            override fun onLoadMore(refreshLayout: RefreshLayout) {
                teacherTaskViewModel.reviewedPage += 1
                teacherTaskViewModel.getReviewedList()
            }
        })
        binding.ivEmpty.singleClick {
            teacherTaskViewModel.reviewedPage = 1
            teacherTaskViewModel.getReviewedList(StateType.DIALOG)
        }
    }

    override fun initObserve() {
        super.initObserve()
        teacherTaskViewModel.reviewedItemInfoLiveData.observe(viewLifecycleOwner) { info ->
            isFirstShow = false
            if ((info == null || info.isEmpty()) && teacherTaskViewModel.reviewedPage == 1) {
                binding.ivEmpty.visible()
                binding.completedListView.gone()
            } else {
                binding.ivEmpty.gone()
                binding.completedListView.visible()
                info?.let {
                    binding.completedListView.setData(
                        info,
                        teacherTaskViewModel.reviewedPage == 1
                    )
                    if (info.size < 10) {
                        binding.refreshLayout.setEnableLoadMore(false)
                    } else {
                        binding.refreshLayout.setEnableLoadMore(true)
                    }
                }
            }
            binding.refreshLayout.finishLoadMore()
            binding.refreshLayout.finishRefresh()
        }
    }

    fun setSearchStudentName(studentName: String? = null) {
        cacheStudentName = studentName
        runCatching {
            teacherTaskViewModel.studentName = studentName
        }
    }

    fun refreshData(resetPage: Boolean = false) {
        if (!isFirstShow) {
            if (resetPage) {
                teacherTaskViewModel.reviewedPage = 1
            }
            teacherTaskViewModel.getReviewedList()
        }
    }

    override fun onRetry() {
        super.onRetry()
        teacherTaskViewModel.getReviewedList(StateType.PAGE)
    }
}