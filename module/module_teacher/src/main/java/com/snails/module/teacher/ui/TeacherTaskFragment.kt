package com.snails.module.teacher.ui

import androidx.fragment.app.activityViewModels
import com.blankj.utilcode.util.KeyboardUtils
import com.snails.base.utils.ext.gone
import com.snails.base.utils.ext.singleClick
import com.snails.base.utils.ext.visible
import com.snails.module.base.utils.SnailTextWatcher
import com.snails.module.teacher.adapter.TaskFragmentStateAdapter
import com.snails.module.teacher.base.BaseTeacherTabFragment
import com.snails.module.teacher.bean.ReviewedTabInfo
import com.snails.module.teacher.bean.Tab
import com.snails.module.teacher.databinding.FragmentTeacherTaskBinding
import com.snails.module.teacher.ui.task.CompletedFragment
import com.snails.module.teacher.ui.task.NotCompleteFragment
import com.snails.module.teacher.viewmodel.TeacherMainViewModel
import com.snails.module.teacher.widget.ReviewedTabView.ItemClickListener

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年01月07日 10:10:13
 */
class TeacherTaskFragment : BaseTeacherTabFragment<FragmentTeacherTaskBinding>() {

    private var currentSelectIndex = 0
    private val teacherMainViewModel: TeacherMainViewModel by activityViewModels()

    private val notCompleteFragment = NotCompleteFragment {
        teacherMainViewModel.notReviewedItemCount.value = it
    }

    private val completedFragment = CompletedFragment()

    private val fragments = listOf(notCompleteFragment, completedFragment)

    private val fragmentAdapter by lazy { TaskFragmentStateAdapter(fragments, this) }

    override fun currentTab() = Tab.TASK

    override fun initView() {
        super.initView()
        binding.reviewedTabView.setTabList(getTabData())

        binding.vp.isUserInputEnabled = false
        binding.vp.adapter = fragmentAdapter
    }

    override fun initClick() {
        super.initClick()
        binding.apply {
            reviewedTabView.itemClickListener = object : ItemClickListener {
                override fun itemClick(index: Int, data: ReviewedTabInfo) {
                    binding.vp.setCurrentItem(index, true)
                    currentSelectIndex = index
                    if (index == 0) {
                        notCompleteFragment.refreshData(true)
                    } else {
                        completedFragment.refreshData(true)
                    }
                }
            }

            tvSearch.singleClick {
                val name = etName.text?.toString()?.trim()
                notCompleteFragment.setSearchStudentName(name)
                completedFragment.setSearchStudentName(name)
                //通知待点评和已点评页面刷新数据
                notCompleteFragment.refreshData(true)
                completedFragment.refreshData(true)
                KeyboardUtils.hideSoftInput(it)
            }

            ivClean.singleClick {
                etName.text?.clear()
            }

            // 监听输入框内容变化
            etName.addTextChangedListener(object : SnailTextWatcher() {
                override fun onTextChanged(
                    s: CharSequence?, start: Int, before: Int, count: Int
                ) {
                    val notCompleteFragment = getNotCompleteFragment()
                    val completedFragment = getCompletedFragment()
                    // 输入框不为空时显示删除按钮，否则隐藏
                    if (s.isNullOrEmpty()) {
                        ivClean.gone()
                        notCompleteFragment?.setSearchStudentName(null)
                        completedFragment?.setSearchStudentName(null)
                        //通知待点评和已点评页面刷新数据
                        notCompleteFragment?.refreshData(true)
                        completedFragment?.refreshData(true)
                    } else {
                        ivClean.visible()
                    }
                }
            })
        }
    }

    private fun getTabData(): List<ReviewedTabInfo> {
        val list = mutableListOf<ReviewedTabInfo>()
        list.add(ReviewedTabInfo(0, "待点评", true))
        list.add(ReviewedTabInfo(1, "已点评", false))
        return list
    }

    override fun fragmentVisibilityChange(visible: Boolean) {
        super.fragmentVisibilityChange(visible)
        if (visible) {
            if (!pageIsFirstShow) {
                if (currentSelectIndex == 0) {
                    notCompleteFragment.refreshData(true)
                } else {
                    completedFragment.refreshData(true)
                }
            }
            pageIsFirstShow = false
        }
    }

    private fun getNotCompleteFragment(): NotCompleteFragment? {
        // ViewPager2 默认 tag 规则："f" + position
        return childFragmentManager.findFragmentByTag("f0") as? NotCompleteFragment
    }

    private fun getCompletedFragment(): CompletedFragment? {
        // ViewPager2 默认 tag 规则："f" + position
        return childFragmentManager.findFragmentByTag("f1") as? CompletedFragment
    }
}