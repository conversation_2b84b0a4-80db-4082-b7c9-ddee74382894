package com.snails.module.teacher.viewbinder

import android.annotation.SuppressLint
import com.snails.base.image_loader.load
import com.snails.base.network.repository.info.teacher.NoCheckInStudentInfo
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.teacher.databinding.ItemNoCheckInLayoutBinding

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2025年01月09日 11:13:24
 */
class NoCheckInViewBinder :
    ViewBindingDelegate<NoCheckInStudentInfo, ItemNoCheckInLayoutBinding>() {

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ItemNoCheckInLayoutBinding>, item: NoCheckInStudentInfo
    ) {
        holder.binding.apply {
            sivStudentPic.load(item.studentAvatar)
            tvStudentName.text = item.studentName ?: ""
            tvStudentClassInfo.text = item.className ?: ""
        }
    }
}