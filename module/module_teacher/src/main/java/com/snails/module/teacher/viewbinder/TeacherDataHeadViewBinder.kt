package com.snails.module.teacher.viewbinder

import android.annotation.SuppressLint
import com.snails.module.base.utils.ViewBindingDelegate
import com.snails.module.base.utils.ViewBindingViewHolder
import com.snails.module.teacher.bean.TeacherHeadBean
import com.snails.module.teacher.databinding.ItemTeacherDataHeadBinding

/**
 * @Description 学习数据-空数据
 * <AUTHOR>
 * @CreateTime 2024年11月27日 19:27:03
 */
class TeacherDataHeadViewBinder :
    ViewBindingDelegate<TeacherHeadBean, ItemTeacherDataHeadBinding>() {

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewBindingViewHolder<ItemTeacherDataHeadBinding>,
        item: TeacherHeadBean
    ) {
    }
}