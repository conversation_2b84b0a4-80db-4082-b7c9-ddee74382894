plugins {
    alias(libs.plugins.common.gradle)
}

android {
    defaultConfig {
        ndk {
            abiFilters.addAll(arrayOf("arm64-v8a"))
        }
    }
    sourceSets {
        getByName("main") {
            jniLibs.srcDir("src/main/jniLibs")
        }
    }
}

dependencies {
    implementation(project(":base:base_log"))
    implementation(project(":base:base_lame"))
    implementation("com.github.SheTieJun.Mp3Recorder:recorder-core:1.9.3")//必选+（下面3个至少选一个）
    implementation("com.github.SheTieJun.Mp3Recorder:recorder-sim:1.9.3")//可选
}