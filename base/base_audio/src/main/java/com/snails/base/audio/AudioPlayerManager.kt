package com.snails.base.audio

import android.content.ComponentName
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.session.MediaController
import androidx.media3.session.SessionToken
import com.blankj.utilcode.util.Utils
import com.google.common.util.concurrent.ListenableFuture
import com.google.common.util.concurrent.MoreExecutors
import com.snails.base.audio.bean.CurrentPlayInfo
import com.snails.base.audio.bean.PlayState
import com.snails.base.audio.bean.PlayTimerType
import com.snails.base.audio.bean.RepeatMode
import com.snails.base.audio.interfaces.IPlayController
import com.snails.base.audio.interfaces.IPlayProgressListener
import com.snails.base.audio.interfaces.IPlayStateListener
import com.snails.base.audio.interfaces.IPlayTimeListener
import com.snails.base.audio.interfaces.IPlayingListener
import com.snails.base.audio.service.MusicService
import com.snails.base.utils.utils.TimerTaskManager
import com.snails.base.utils.utils.TimesUtils

/**
 * @Description 音频播放管理器
 * <AUTHOR>
 * @CreateTime 2024年11月11日 14:20:35
 */
object AudioPlayerManager : IPlayController {

    private var isInitialized = false //是否进行了初始化
    var playState = PlayState.IDLE
    var forcePlay = false //强制播放
    private var currentPlayMediaMetadata: MediaMetadata? = null //当前正在播放的元数据
    private var mediaController: MediaController? = null
    private var onControllerReady: (() -> Unit)? = null
    private var timerTaskManager: TimerTaskManager? = null //播放器定时器
    private var playTimerType: PlayTimerType? = null //定时播放类型
    private var timerPlayCount = 0 //定时播放集数
    private val mediaItemPlayList = mutableListOf<MediaItem>() //当前播放的列表数据

    private var controllerFuture: ListenableFuture<MediaController>? = null

    private var playStateListener = mutableSetOf<IPlayStateListener>()
    private var playProgressListener = mutableSetOf<IPlayProgressListener>()
    private var playingListener = mutableSetOf<IPlayingListener>()
    private var playTimeListener = mutableSetOf<IPlayTimeListener>()

    private val handler = Handler(Looper.getMainLooper())

    fun checkPlaybackPosition(delayMs: Long): Boolean = handler.postDelayed(
        {
            //播放进度
            mediaController?.currentPosition?.let { currentPosition ->
                val duration = mediaController?.duration ?: 0
                val progressPercentage = if (duration > 0) {
                    (currentPosition * 100 / duration).toInt()
                } else {
                    0 // 如果duration未知或为0，默认为0%
                }
                // 获取当前播放时长和音频总时长
                val currentTime = mediaController?.currentPosition ?: 0
                //获取当前播放音频总时长
                val durationTime = mediaController?.duration ?: 0
                playTimeListener.forEach {
                    it.totalTime(durationTime, TimesUtils.formatTime(durationTime))
                    it.currentTime(TimesUtils.formatTime(currentTime))
                }
                playProgressListener.forEach {
                    it.playProgress(progressPercentage)
                }
            }
            //缓冲进度
            mediaController?.bufferedPercentage?.let { currentBufferedPosition ->
                playProgressListener.forEach {
                    it.bufferProgress(currentBufferedPosition)
                }
            }
            checkPlaybackPosition(delayMs)
        }, delayMs
    )

    fun init(onReady: (() -> Unit)? = null) {
        // 防止重复初始化
        if (isInitialized) {
            onReady?.invoke()
            return
        }
        val ctx = Utils.getApp() ?: return
        isInitialized = true
        onControllerReady = onReady

        val component = ComponentName(ctx, MusicService::class.java)
        val sessionToken = SessionToken(ctx, component)
        controllerFuture = MediaController.Builder(ctx, sessionToken).buildAsync()
        controllerFuture?.addListener(
            {
                mediaController = controllerFuture?.get()
                onControllerReady?.invoke() // 通知控制器已准备好
                mediaController?.apply {
                    addListener(object : Player.Listener {
                        override fun onIsPlayingChanged(isPlaying: Boolean) {
                            playingListener.forEach {
                                it.onIsPlayingChanged(isPlaying)
                            }
                            if (isPlaying) {
                                notifyPlayState(PlayState.PLAYING)
                            } else {
                                notifyPlayState(PlayState.PAUSE)
                            }
                        }

                        //播放状态更改
                        override fun onPlaybackStateChanged(playbackState: Int) {
                            when (playbackState) {
                                Player.STATE_READY -> {
                                    notifyPlayState(PlayState.READY)
                                }

                                Player.STATE_BUFFERING -> {
                                    notifyPlayState(PlayState.BUFFERING)
                                }

                                Player.STATE_ENDED -> {
                                    Log.d("TAG_HQL", "onPlaybackStateChanged: STATE_ENDED")
                                    notifyPlayState(PlayState.FINISH)
                                }

                                Player.STATE_IDLE -> {
                                    notifyPlayState(PlayState.IDLE)
                                }
                            }
                            //Player.STATE_IDLE：这是初始状态，即播放器 以及停止播放的时间玩家只会拥有有限的资源 状态
                            //Player.STATE_BUFFERING：播放器无法立即从其 当前位置。这主要是因为需要加载更多数据。
                            //Player.STATE_READY：播放器能够立即从当前播放的内容开始播放 排名。
                            //Player.STATE_ENDED：播放器已播放完所有媒体。
                        }

                        override fun onPlayerError(error: PlaybackException) {
                            super.onPlayerError(error)
                            //导致播放失败的错误可以通过 onPlayerError(PlaybackException error) Player.Listener。
                            //发生故障时，将调用此方法 播放状态转换为 Player.STATE_IDLE 之前立即触发。
                            //失败或停止的播放可以调用 ExoPlayer.prepare 进行重试。
                            runCatching {
                                notifyPlayState(PlayState.ERROR)
                            }
                        }

                        override fun onMediaMetadataChanged(mediaMetadata: MediaMetadata) {
                            super.onMediaMetadataChanged(mediaMetadata)
                            checkCountAndStopPlay()
                            currentPlayMediaMetadata = mediaMetadata
                            playingListener.forEach {
                                it.currentPlayMediaMetadata(mediaMetadata)
                            }
                        }
                    })
                }
            }, MoreExecutors.directExecutor()
        )
    }

    override fun play(url: String, speed: Float) {
        reallyPlay(url, speed)
    }

    override fun play(url: String) {
        reallyPlay(url)
    }

    private fun reallyPlay(url: String, speed: Float = 1.0f) {
        val mediaItem = MediaItem.fromUri(url)
        mediaController?.apply {
            setPlaybackSpeed(speed)
            clearMediaItems()
            addMediaItem(mediaItem)
            prepare()
            play()
        }
    }

    override fun play(mediaItems: List<MediaItem>) {
        mediaItemPlayList.clear()
        mediaItemPlayList.addAll(mediaItems)
        mediaController?.apply {
            clearMediaItems()
            addMediaItems(mediaItems)
            prepare()
            play()
        }
    }

    override fun setSpeed(speed: Float) {
        mediaController?.apply {
            pause()
            setPlaybackSpeed(speed)
            resume()
        }
    }

    override fun playPos(pos: Int) {
        mediaController?.seekTo(pos, 0)
    }

    override fun resume() {
        mediaController?.play()
    }

    override fun stop() {
        mediaController?.stop()
        notifyPlayState(PlayState.STOP)
    }

    override fun pause() {
        mediaController?.pause()
    }

    override fun release() {
        mediaController?.stop()
        Utils.getApp()?.let {
            it.stopService(Intent(it, MusicService::class.java)) // 停止后台服务
        }
        controllerFuture?.let {
            MediaController.releaseFuture(it)
        }
        mediaController?.release()
        timerTaskManager?.timerListener = null
        timerTaskManager?.stop()
        isInitialized = false
        playState = PlayState.IDLE
        currentPlayMediaMetadata = null
        mediaController = null
        onControllerReady = null
        timerTaskManager = null
        playTimerType = null
        timerPlayCount = 0
        mediaItemPlayList.clear()
        controllerFuture = null
    }

    override fun next() {
        val currentIndex = mediaController?.currentMediaItemIndex
        if (currentIndex == mediaItemPlayList.size - 1) {
            // 如果是最后一首，则循环到第一首
            mediaController?.seekTo(0, C.TIME_UNSET)
        } else {
            // 否则播放下一首
            mediaController?.seekToNextMediaItem()
        }
    }

    override fun previous() {
        val currentIndex = mediaController?.currentMediaItemIndex ?: return
        if (mediaItemPlayList.isEmpty()) {
            // 如果播放列表为空，直接返回
            return
        }
        val previousIndex = if (currentIndex == 0) {
            // 如果是第一首，则循环到最后一首
            mediaItemPlayList.size - 1
        } else {
            // 否则播放上一首
            currentIndex - 1
        }
        // 调用 seekTo 时确保索引有效
        mediaController?.seekTo(previousIndex, C.TIME_UNSET)
    }

    /**
     * 设置重复模式
     * @param mode 1:单曲循环，2：列表循环
     */
    fun setRepeatMode(mode: RepeatMode) {
        if (mode == RepeatMode.REPEAT_MODE_ONE) {
            mediaController?.repeatMode = Player.REPEAT_MODE_ONE
            return
        }
        if (mode == RepeatMode.REPEAT_MODE_OFF) {
            mediaController?.repeatMode = Player.REPEAT_MODE_OFF
            return
        }
        mediaController?.repeatMode = Player.REPEAT_MODE_ALL
    }

    /**
     * 获取当前重复模式
     */
    fun getRepeatMode(): RepeatMode {
        val mode = mediaController?.repeatMode ?: 1
        if (mode == 1) {
            return RepeatMode.REPEAT_MODE_ONE
        }
        return RepeatMode.REPEAT_MODE_ALL
    }

    /**
     * seek到某个位置。百分比
     */
    fun seekTo(progress: Int?) {
        val pro = progress ?: return
        val duration = mediaController?.duration ?: 0
        if (duration <= 0) {
            return
        }
        val pos = (pro.toFloat() / 100f) * duration
        mediaController?.seekTo(pos.toLong())
    }

    /**
     * 跳转到指定Item
     */
    fun seekToIndex(index: Int) {
        if (index < 0) {
            return
        }
        mediaController?.seekTo(index, C.TIME_UNSET)
    }

    /**
     * 设置定时任务类型
     */
    fun setTimer(type: PlayTimerType) {
        playTimerType = type
        if (type == PlayTimerType.MINUTES_15 || type == PlayTimerType.MINUTES_30 || type == PlayTimerType.MINUTES_60) {
            startTimer(getMinutes(type))
            return
        }
        stopTimer()
        timerPlayCount = getCount(type)
    }

    /**
     * 清除定时任务类型
     */
    fun clearPlayTimerType() {
        playTimerType = null
    }

    /**
     * 设置定时器监听器
     */
    fun setTimerListener(listener: TimerTaskManager.TimerListener) {
        if (timerTaskManager == null) {
            timerTaskManager = TimerTaskManager()
        }
        timerTaskManager?.timerListener = listener
    }

    /**
     * 移除定时器监听器
     */
    fun removeTimerListener() {
        timerTaskManager?.timerListener = null
    }

    fun addPlayStateListener(listener: IPlayStateListener) {
        playStateListener.add(listener)
    }

    fun removePlayStateListener(listener: IPlayStateListener) {
        playStateListener.remove(listener)
    }

    fun addPlayProgressListener(listener: IPlayProgressListener) {
        playProgressListener.add(listener)
    }

    fun removePlayProgressListener(listener: IPlayProgressListener) {
        playProgressListener.remove(listener)
    }

    fun addPlayingListener(listener: IPlayingListener) {
        playingListener.add(listener)
    }

    fun removePlayingListener(listener: IPlayingListener) {
        playingListener.remove(listener)
    }

    fun addPlayTimeListener(listener: IPlayTimeListener) {
        playTimeListener.add(listener)
    }

    fun removePlayTimeListener(listener: IPlayTimeListener) {
        playTimeListener.remove(listener)
    }

    /**
     * 主动查询一下，当前正在播放的信息
     */
    fun queryPlayingInfo(): CurrentPlayInfo {
        return CurrentPlayInfo(
            isPlaying = playState == PlayState.PLAYING,
            playTimerType = playTimerType,
            mediaMetadata = currentPlayMediaMetadata
        )
    }

    fun getControllerFuture() = controllerFuture

    private fun notifyPlayState(state: PlayState) {
        playState = state
        if (playStateListener.isEmpty()) {
            return
        }
        playStateListener.forEach {
            when (state) {
                PlayState.PLAYING -> it.playing()
                PlayState.BUFFERING -> it.buffering()
                PlayState.PAUSE -> it.pause()
                PlayState.STOP -> it.stop()
                PlayState.IDLE,
                PlayState.READY,
                PlayState.FINISH -> {
                }

                PlayState.ERROR -> {
                    it.error()
                }
            }
        }
    }

    private fun startTimer(time: Int) {
        if (timerTaskManager == null) {
            timerTaskManager = TimerTaskManager()
        }
        timerTaskManager?.start(time)
    }

    /**
     * 检查集数倒计时，是否需要停止播放
     */
    private fun checkCountAndStopPlay() {
        if (playTimerType != null && timerPlayCount != 0) {
            timerPlayCount--
            if (timerPlayCount <= 0) {
                timerTaskManager?.timerListener?.onFinish()
                playTimerType = null
                pause()
            }
        }
    }

    /**
     * 停止定时器监听器
     */
    fun stopTimer() {
        timerTaskManager?.stop()
    }

    /**
     * 停止获取播放进度时间
     */
    fun stopCheckingPlaybackPosition() {
        handler.removeCallbacksAndMessages(null)
    }

    private fun getMinutes(type: PlayTimerType): Int {
        return when (type) {
            PlayTimerType.MINUTES_15 -> 15
            PlayTimerType.MINUTES_30 -> 30
            PlayTimerType.MINUTES_60 -> 60
            else -> 0
        }
    }

    private fun getCount(type: PlayTimerType): Int {
        return when (type) {
            PlayTimerType.COUNT_1 -> 1
            PlayTimerType.COUNT_3 -> 3
            PlayTimerType.COUNT_5 -> 5
            else -> -1
        }
    }
}