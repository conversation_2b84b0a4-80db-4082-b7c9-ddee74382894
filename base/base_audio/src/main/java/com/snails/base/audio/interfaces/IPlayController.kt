package com.snails.base.audio.interfaces

import androidx.media3.common.MediaItem

/**
 * @Description 播放控制接口
 * <AUTHOR>
 * @CreateTime 2024年11月11日 14:22:25
 */
interface IPlayController {

    fun play(url: String, speed: Float)

    fun play(url: String)

    fun setSpeed(speed: Float)

    fun play(mediaItems: List<MediaItem>)

    fun playPos(pos: Int)

    fun resume()

    fun stop()

    fun pause()

    fun release()

    fun previous()

    fun next()
}