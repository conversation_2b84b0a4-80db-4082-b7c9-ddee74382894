package com.snails.base.audio.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.session.MediaSession
import androidx.media3.session.MediaSessionService

class MusicService : MediaSessionService() {

    private var player: Player? = null
    private var mediaSession: MediaSession? = null
    private val CHANNEL_ID = "MusicServiceChannel"
    private val NOTIFICATION_ID = 1
    private var openPageClassName = "com.snails.module.audio.AudioActivity"

    override fun onCreate() {
        super.onCreate()
        try {
            // 创建通知渠道（Android 8.0 及以上）
            val channel = NotificationChannel(
                CHANNEL_ID,
                "音频播放服务通知",
                NotificationManager.IMPORTANCE_LOW
            )
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)

            // 初始化 ExoPlayer 和 MediaSession
            player = ExoPlayer.Builder(this).build()
            mediaSession = MediaSession.Builder(this, player!!)
                .apply {
                    generateOpenActivityPendingIntent(
                        this@MusicService,
                        openPageClassName
                    )?.let {
                        setSessionActivity(it)
                    }
                }
                .build()

            // 创建 MediaStyle 通知
            val notification = NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("故事播放")
                .setContentText("正在播放...")
                .setContentIntent(mediaSession?.sessionActivity)
                .build()

            // 启动前台服务
            startForeground(NOTIFICATION_ID, notification)
        } catch (t: Throwable) {
            t.printStackTrace()
        }
    }

    private fun generateOpenActivityPendingIntent(
        context: Context,
        activityClassName: String
    ): PendingIntent? {
        return try {
            val openClass = Class.forName(activityClassName)
            val openUI = Intent(context, openClass)
            openUI.flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
            openUI.putExtra("RE_ENTER_FROM_AUDIO", true)
            PendingIntent.getActivity(
                context,
                1,
                openUI,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
        } catch (e: Exception) {
            null
        }
    }

    override fun onTaskRemoved(rootIntent: Intent?) {
        val player = mediaSession?.player ?: return
        if (!player.playWhenReady
            || player.mediaItemCount == 0
            || player.playbackState == Player.STATE_ENDED
        ) {
            player.pause()
        }
        stopForeground(true) // 移除通知
        stopSelf()
    }

    override fun onDestroy() {
        player?.release()
        mediaSession?.release()
        super.onDestroy()
    }

    override fun onGetSession(controllerInfo: MediaSession.ControllerInfo) = mediaSession
}