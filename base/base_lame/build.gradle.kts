plugins {
    alias(libs.plugins.common.gradle)
}
android {
    defaultConfig {
        ndk {
            abiFilters.addAll(arrayOf("arm64-v8a"))
        }
        //解决.c 里面的警告
        externalNativeBuild {
            cmake {
                cFlags("-DSTDC_HEADERS")
            }
        }
    }
    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
        }
    }
}