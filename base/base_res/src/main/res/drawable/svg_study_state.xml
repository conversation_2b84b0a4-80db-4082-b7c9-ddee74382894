<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="160dp"
    android:height="128dp"
    android:viewportWidth="160"
    android:viewportHeight="128">
  <path
      android:pathData="M119.82,86.86H38.89L25.33,101L130.35,99.31L119.82,86.86Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.03127"
      android:fillColor="#BAD1F5"
      android:strokeColor="#97B1DE"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M28.94,115.32H129.96C130.06,115.32 130.11,115.18 130.06,115.08C128.28,110.18 128.28,104.5 130.06,99.6C130.11,99.5 130.06,99.36 129.96,99.36H28.99C25.57,99.36 22.79,102.92 22.79,107.34C22.79,111.77 25.53,115.32 28.94,115.32Z"
      android:fillColor="#EBF2FF"/>
  <path
      android:pathData="M94.48,104.07H129.1"
      android:strokeLineJoin="round"
      android:strokeWidth="1.02868"
      android:fillColor="#00000000"
      android:strokeColor="#D2E0F7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.42,104.07H79.04"
      android:strokeLineJoin="round"
      android:strokeWidth="1.02868"
      android:fillColor="#00000000"
      android:strokeColor="#D2E0F7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M34.52,111.72H129.19"
      android:strokeLineJoin="round"
      android:strokeWidth="1.2374"
      android:fillColor="#00000000"
      android:strokeColor="#D2E0F7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M51.01,108.01H112.6"
      android:strokeLineJoin="round"
      android:strokeWidth="1.02747"
      android:fillColor="#00000000"
      android:strokeColor="#D2E0F7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M29.13,115.28H130.15C130.25,115.28 130.3,115.13 130.25,115.04C128.47,110.13 128.47,104.46 130.25,99.55C130.3,99.46 130.25,99.31 130.15,99.31H29.18C25.77,99.31 22.98,102.87 22.98,107.29C22.98,111.72 25.72,115.28 29.13,115.28Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.02868"
      android:fillColor="#00000000"
      android:strokeColor="#9BB6E0"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M42.64,77.77H110.3L121.64,86.67L33.85,85.61L42.64,77.77Z"
      android:fillColor="#CDDEFA"/>
  <group>
    <clip-path
        android:pathData="M42.64,77.77H110.3L121.64,86.67L33.85,85.61L42.64,77.77Z"/>
    <path
        android:pathData="M116.11,82.77H37.4C36.44,82.77 35.67,82 35.67,81.04V80.66C35.67,79.7 36.44,78.93 37.4,78.93H116.11C117.08,78.93 117.85,79.7 117.85,80.66V81.04C117.85,81.96 117.08,82.77 116.11,82.77Z"
        android:fillColor="#99B3DE"/>
  </group>
  <path
      android:pathData="M42.64,77.77H110.3L121.64,86.67L33.85,85.61L42.64,77.77Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.746335"
      android:fillColor="#00000000"
      android:strokeColor="#AEC4E8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.47,95.61H33.99C33.89,95.61 33.84,95.51 33.89,95.47C35.38,92.39 35.38,88.83 33.89,85.75C33.84,85.71 33.89,85.61 33.99,85.61H118.42C121.31,85.61 123.61,87.87 123.61,90.61C123.61,93.35 121.31,95.61 118.47,95.61Z"
      android:fillColor="#EBF2FF"/>
  <path
      android:pathData="M63.85,88.59H34.9"
      android:strokeLineJoin="round"
      android:strokeWidth="0.74446"
      android:fillColor="#00000000"
      android:strokeColor="#D2E0F7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M105.68,88.59H76.73"
      android:strokeLineJoin="round"
      android:strokeWidth="0.74446"
      android:fillColor="#00000000"
      android:strokeColor="#D2E0F7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M114,93.4H58.99"
      android:strokeLineJoin="round"
      android:strokeWidth="0.746335"
      android:fillColor="#00000000"
      android:strokeColor="#D2E0F7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M86.5,91.04H35"
      android:strokeLineJoin="round"
      android:strokeWidth="0.743595"
      android:fillColor="#00000000"
      android:strokeColor="#D2E0F7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M118.47,95.61H33.99C33.89,95.61 33.84,95.51 33.89,95.47C35.38,92.39 35.38,88.83 33.89,85.75C33.84,85.71 33.89,85.61 33.99,85.61H118.42C121.31,85.61 123.61,87.87 123.61,90.61C123.61,93.35 121.31,95.61 118.47,95.61Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.74446"
      android:fillColor="#00000000"
      android:strokeColor="#AEC4E8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M104.43,98.06L99.62,96.33L94.91,98.06V88.59H104.43V98.06Z"
      android:fillColor="#CFE2FF"/>
  <path
      android:pathData="M63.61,114.07L58.56,112.25L53.56,114.07V104.07H63.61V114.07Z"
      android:fillColor="#B8CFF0"/>
  <path
      android:pathData="M121.84,38.34C121.84,38.34 121.45,50.17 129.05,57.1L146.79,56.95C146.79,56.95 137.75,46.61 137.22,39.4L121.84,38.34Z"
      android:fillColor="#BAD1F5"/>
  <path
      android:pathData="M31.3,59.4C31.3,59.4 29.66,68.54 24.18,72.92L13.17,70.42C13.17,70.42 19.9,63.54 20.96,57.96L31.3,59.4Z"
      android:fillColor="#CFE2FF"/>
  <path
      android:pathData="M56.83,39.11L34.23,44.11C32.4,44.5 30.58,43.34 30.19,41.52L27.4,28.97C27.02,27.14 28.17,25.31 30,24.93L52.55,19.93C54.38,19.54 56.2,20.7 56.59,22.53L59.38,35.07C59.81,36.85 58.66,38.68 56.83,39.11Z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.9233"
      android:fillColor="#E0ECFF"
      android:strokeColor="#D2E0F7"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M44.04,23.87L39.37,24.93C38.99,25.03 38.61,24.78 38.56,24.4L37.98,21.8C37.88,21.42 38.12,21.03 38.51,20.99L43.17,19.93C43.56,19.83 43.94,20.07 43.99,20.46L44.57,23.05C44.66,23.44 44.42,23.77 44.04,23.87Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.74446"
      android:fillColor="#AEC4E8"
      android:strokeColor="#AEC4E8"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M53.41,26.9L32.16,31.61C31.78,31.71 31.39,31.47 31.3,31.08C31.2,30.7 31.44,30.31 31.83,30.22L53.13,25.51C53.51,25.41 53.9,25.65 53.99,26.04C54.04,26.42 53.8,26.8 53.41,26.9Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M54.33,31.04L33.03,35.75C32.64,35.84 32.26,35.6 32.16,35.22C32.07,34.83 32.31,34.45 32.69,34.35L53.99,29.64C54.38,29.55 54.76,29.79 54.86,30.17C54.95,30.55 54.71,30.94 54.33,31.04Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M55.24,35.12L33.94,39.83C33.56,39.93 33.17,39.69 33.08,39.31C32.98,38.92 33.22,38.54 33.6,38.44L54.91,33.73C55.29,33.63 55.67,33.87 55.77,34.26C55.87,34.69 55.63,35.07 55.24,35.12Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M47.88,69.12C47.88,69.12 44.57,72.92 41.54,74.69C38.51,76.47 36.83,76.95 37.07,77.34C37.31,77.68 39.9,79.69 45,79.65C50.1,79.6 55.29,79.65 57.31,79.07C59.33,78.49 59.76,72.92 59.47,72.43C59.23,72 55.53,68.2 55.19,68.01"
      android:fillColor="#FFD0B8"/>
  <path
      android:pathData="M48.8,67.53C48.61,67.77 43.17,74.98 38.85,75.9C34.52,76.81 38.32,79.6 45.96,79.6C55.72,79.65 58.85,78.4 64.33,75.7"
      android:strokeWidth="0.992181"
      android:fillColor="#00000000"
      android:strokeColor="#EBB697"/>
  <path
      android:pathData="M70.24,56.9C70.24,56.9 74.09,67.24 65.1,69.21C56.06,71.18 52.16,59.69 54.18,54.21C56.16,48.78 67.26,42.38 76.21,48.35C85.15,54.31 85.2,68.01 77.79,75.03C70.34,82.05 56.4,84.07 46.78,73.73C37.11,63.4 39.13,47.77 46.39,39.83C53.65,31.9 69.28,27.67 79.19,32.48C89.09,37.29 96.02,45.94 95.97,60.17C95.92,74.41 82.41,82 71.64,79.02"
      android:fillColor="#77B2F2"/>
  <group>
    <clip-path
        android:pathData="M70.24,56.9C70.24,56.9 74.09,67.24 65.1,69.21C56.06,71.18 52.16,59.69 54.18,54.21C56.16,48.78 67.26,42.38 76.21,48.35C85.15,54.31 85.2,68.01 77.79,75.03C70.34,82.05 56.4,84.07 46.78,73.73C37.11,63.4 39.13,47.77 46.39,39.83C53.65,31.9 69.28,27.67 79.19,32.48C89.09,37.29 96.02,45.94 95.97,60.17C95.92,74.41 82.41,82 71.64,79.02"/>
    <path
        android:pathData="M39.81,64.89C39.37,64.65 37.74,39.98 59.81,33.92C78.95,28.68 86.78,38.49 93.71,46.81C100.63,55.12 87.89,30.12 87.41,29.59C86.93,29.11 81.01,24.35 77.79,24.4C74.57,24.45 63.18,29.21 61.59,28.73C60,28.25 56.2,30.12 55.48,29.26C54.71,28.39 43.08,37.53 43.08,37.53C43.08,37.53 37.98,45.17 37.21,48.2C36.44,51.23 37.84,57.14 37.64,59.02C37.45,60.94 39.81,64.89 39.81,64.89Z"
        android:strokeAlpha="0.68"
        android:fillColor="#CCDDFF"
        android:fillAlpha="0.68"/>
  </group>
  <group>
    <clip-path
        android:pathData="M70.24,56.9C70.24,56.9 74.09,67.24 65.1,69.21C56.06,71.18 52.16,59.69 54.18,54.21C56.16,48.78 67.26,42.38 76.21,48.35C85.15,54.31 85.2,68.01 77.79,75.03C70.34,82.05 56.4,84.07 46.78,73.73C37.11,63.4 39.13,47.77 46.39,39.83C53.65,31.9 69.28,27.67 79.19,32.48C89.09,37.29 96.02,45.94 95.97,60.17C95.92,74.41 82.41,82 71.64,79.02"/>
    <path
        android:pathData="M54.14,60.56C54.42,60.65 57.55,67.87 65.39,66.04C70.48,64.84 70.24,60.85 70.24,60.85L70.29,65.32L66.73,68.06L62.79,69.12L58.94,68.2C58.94,68.2 56.83,66.38 56.68,66.18C56.54,65.99 54.95,63.35 54.91,63.2C54.81,63.06 54.14,60.56 54.14,60.56Z"
        android:strokeAlpha="0.45"
        android:fillColor="#4886D4"
        android:fillAlpha="0.45"/>
  </group>
  <group>
    <clip-path
        android:pathData="M70.24,56.9C70.24,56.9 74.09,67.24 65.1,69.21C56.06,71.18 52.16,59.69 54.18,54.21C56.16,48.78 67.26,42.38 76.21,48.35C85.15,54.31 85.2,68.01 77.79,75.03C70.34,82.05 56.4,84.07 46.78,73.73C37.11,63.4 39.13,47.77 46.39,39.83C53.65,31.9 69.28,27.67 79.19,32.48C89.09,37.29 96.02,45.94 95.97,60.17C95.92,74.41 82.41,82 71.64,79.02"/>
    <path
        android:pathData="M54.42,53.59C54.42,53.59 57.79,48.87 65,47.91C72.17,47 78.61,50.85 80.68,53.78C82.74,56.71 80.53,52.72 80.53,52.72L74.81,47.48C74.81,47.48 69.95,45.99 69.81,45.99C69.71,45.99 63.8,46.28 63.7,46.33C63.61,46.37 58.94,48.11 58.85,48.2C58.75,48.25 55.58,51.33 55.58,51.33L54.42,53.59Z"
        android:strokeAlpha="0.68"
        android:fillColor="#CCDDFF"
        android:fillAlpha="0.68"/>
  </group>
  <group>
    <clip-path
        android:pathData="M70.24,56.9C70.24,56.9 74.09,67.24 65.1,69.21C56.06,71.18 52.16,59.69 54.18,54.21C56.16,48.78 67.26,42.38 76.21,48.35C85.15,54.31 85.2,68.01 77.79,75.03C70.34,82.05 56.4,84.07 46.78,73.73C37.11,63.4 39.13,47.77 46.39,39.83C53.65,31.9 69.28,27.67 79.19,32.48C89.09,37.29 96.02,45.94 95.97,60.17C95.92,74.41 82.41,82 71.64,79.02"/>
    <path
        android:pathData="M38.89,56.62C39.04,57.38 41.97,73.97 63.46,75.13C76.59,75.85 80.24,71.76 80.24,71.76C80.24,71.76 84.96,73.11 85.92,63.4C86.78,54.64 93.32,48.83 93.32,48.83C93.32,48.83 91.45,83.93 90.97,83.73C90.44,83.59 63.37,83.59 62.45,83.59C61.54,83.59 44.23,76.14 44.04,75.13C43.85,74.12 38.89,56.62 38.89,56.62Z"
        android:strokeAlpha="0.45"
        android:fillColor="#4886D4"
        android:fillAlpha="0.45"/>
  </group>
  <path
      android:pathData="M62.69,81.38C62.45,81.38 62.21,81.38 62.02,81.38C56.06,81.19 50.67,78.69 46.39,74.12C41.87,69.31 39.57,62.87 39.81,55.99C40.05,49.79 42.31,43.83 46.01,39.59C49.9,35.12 55.67,31.95 62.26,30.65C68.42,29.4 74.67,29.98 79.33,32.14C84.76,34.69 88.95,38.2 91.83,42.67C94.96,47.48 96.5,53.44 96.4,60.32C96.35,66.81 93.56,72.43 88.56,76.19C86.16,78.01 83.27,79.26 80.34,79.89C77.41,80.46 74.38,80.42 71.64,79.65C68.8,80.75 65.72,81.38 62.69,81.38ZM68.61,30.89C60.87,30.89 52.26,33.82 46.73,40.17C39.62,48.35 37.69,63.35 47.12,73.4C52.31,78.93 58.08,80.22 62.07,80.37C67.7,80.56 73.46,78.4 77.46,74.65C81.16,71.18 83.08,65.9 82.6,60.51C82.17,55.56 79.76,51.28 75.92,48.73C71.78,45.99 66.78,45.36 62.21,47.05C58.46,48.39 55.53,51.23 54.62,54.36C53.7,57.53 54.47,62.87 57.41,65.99C59.33,68.06 61.93,68.83 64.86,68.2C67.12,67.72 68.66,66.81 69.43,65.46C70.34,63.92 70.1,62.1 69.71,60.85C69.23,59.12 68.08,58.78 68.08,58.78C67.84,58.73 67.65,58.44 67.74,58.2C67.79,57.96 68.08,57.77 68.32,57.87C68.37,57.87 70,58.3 70.68,60.61C71.25,62.67 71.16,64.55 70.29,65.99C69.38,67.53 67.6,68.64 65.1,69.17C61.78,69.89 58.9,69.02 56.68,66.66C53.51,63.25 52.65,57.58 53.66,54.12C54.66,50.65 57.74,47.67 61.88,46.18C66.73,44.4 72.07,45.08 76.45,48.01C80.53,50.7 83.08,55.27 83.56,60.51C84.04,66.18 81.98,71.76 78.08,75.46C76.59,76.86 74.86,78.06 73.03,78.97C78.13,79.98 83.61,78.69 87.99,75.42C92.75,71.86 95.39,66.47 95.44,60.32C95.58,47.38 90.01,38.25 78.95,33.06C75.97,31.61 72.41,30.89 68.61,30.89Z"
      android:fillColor="#3678CE"/>
  <path
      android:pathData="M84.81,45.12C84.28,45.12 83.75,44.88 83.37,44.45C78.47,38.63 71.06,38.54 70.96,38.54C69.95,38.54 69.09,37.72 69.09,36.66C69.09,35.65 69.91,34.79 70.96,34.79C71.35,34.79 80.2,34.88 86.21,42C86.88,42.77 86.78,43.97 85.97,44.64C85.63,44.98 85.2,45.12 84.81,45.12Z"
      android:strokeAlpha="0.47"
      android:fillAlpha="0.47">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="69.16"
          android:startY="35.96"
          android:endX="86.98"
          android:endY="42.98"
          android:type="linear">
        <item android:offset="0.05" android:color="#FFFFFFFF"/>
        <item android:offset="1" android:color="#19E1F7FF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M65,38.82C63.99,38.82 63.13,38.06 63.08,37C63.03,35.94 63.8,35.03 64.91,34.93H65.15C66.2,34.88 67.12,35.65 67.21,36.76C67.26,37.82 66.49,38.73 65.39,38.82H65.15C65.05,38.82 65.05,38.82 65,38.82Z"
      android:strokeAlpha="0.47"
      android:fillColor="#ffffff"
      android:fillAlpha="0.47"/>
  <path
      android:pathData="M106.98,36.85L108.71,22.19C108.71,22.19 107.12,20.7 107.65,17.57C108.18,14.45 109.96,13.73 111.26,13.82C112.89,13.97 114.33,15.46 114.14,18.49C113.9,21.95 112.07,22.72 112.07,22.72L111.59,36.76L106.98,36.85Z"
      android:fillColor="#F9E1D5"/>
  <group>
    <clip-path
        android:pathData="M106.98,36.85L108.71,22.19C108.71,22.19 107.12,20.7 107.65,17.57C108.18,14.45 109.96,13.73 111.26,13.82C112.89,13.97 114.33,15.46 114.14,18.49C113.9,21.95 112.07,22.72 112.07,22.72L111.59,36.76L106.98,36.85Z"/>
    <path
        android:pathData="M107.89,29.74C107.89,29.74 110.54,32.57 113.81,31.42C117.08,30.27 112.51,35.41 112.32,35.41C112.12,35.41 106.11,35.03 106.11,34.55C106.11,34.02 105.15,32.24 105.39,31.71C105.63,31.18 107.89,29.74 107.89,29.74Z"
        android:fillColor="#FFCEAE"/>
  </group>
  <group>
    <clip-path
        android:pathData="M106.98,36.85L108.71,22.19C108.71,22.19 107.12,20.7 107.65,17.57C108.18,14.45 109.96,13.73 111.26,13.82C112.89,13.97 114.33,15.46 114.14,18.49C113.9,21.95 112.07,22.72 112.07,22.72L111.59,36.76L106.98,36.85Z"/>
    <path
        android:pathData="M108.61,15.6C108.61,15.6 110.78,14.5 112.27,15.99C113.81,17.52 113.47,19.69 112.99,21.27C112.51,22.86 115.3,19.4 115.44,18.92C115.54,18.44 114.82,15.55 114.33,14.98C113.85,14.4 112.6,13.77 112.12,13.63C111.64,13.44 110.25,13.24 109.72,13.44C109.24,13.63 108.57,14.64 108.52,14.88C108.47,15.12 108.42,15.5 108.42,15.5"
        android:fillColor="#FFFAFA"/>
  </group>
  <path
      android:pathData="M106.98,36.85L108.71,22.19C108.71,22.19 107.12,20.7 107.65,17.57C108.18,14.45 109.96,13.73 111.26,13.82C112.89,13.97 114.33,15.46 114.14,18.49C113.9,21.95 112.07,22.72 112.07,22.72L111.59,36.76L106.98,36.85Z"
      android:strokeWidth="0.992181"
      android:fillColor="#00000000"
      android:strokeColor="#EBB697"/>
  <path
      android:pathData="M97.79,36.42L97.51,21.66C97.51,21.66 95.78,20.41 95.82,17.24C95.92,14.06 97.6,13.1 98.85,13.01C100.49,12.91 102.12,14.21 102.31,17.24C102.56,20.7 100.87,21.71 100.87,21.71L102.31,35.65L97.79,36.42Z"
      android:fillColor="#F9E1D5"/>
  <group>
    <clip-path
        android:pathData="M97.79,36.42L97.51,21.66C97.51,21.66 95.78,20.41 95.82,17.24C95.92,14.06 97.6,13.1 98.85,13.01C100.49,12.91 102.12,14.21 102.31,17.24C102.56,20.7 100.87,21.71 100.87,21.71L102.31,35.65L97.79,36.42Z"/>
    <path
        android:pathData="M97.7,29.26C97.7,29.26 100.73,31.71 103.81,30.12C106.88,28.54 103.08,34.26 102.89,34.31C102.7,34.35 96.69,34.79 96.59,34.26C96.5,33.73 95.29,32.09 95.49,31.56C95.68,30.99 97.7,29.26 97.7,29.26Z"
        android:fillColor="#FFCEAE"/>
  </group>
  <group>
    <clip-path
        android:pathData="M97.79,36.42L97.51,21.66C97.51,21.66 95.78,20.41 95.82,17.24C95.92,14.06 97.6,13.1 98.85,13.01C100.49,12.91 102.12,14.21 102.31,17.24C102.56,20.7 100.87,21.71 100.87,21.71L102.31,35.65L97.79,36.42Z"/>
    <path
        android:pathData="M96.5,15.17C96.5,15.17 98.47,13.77 100.2,15.07C101.93,16.37 101.88,18.58 101.64,20.22C101.35,21.85 103.66,18.05 103.71,17.57C103.76,17.09 102.6,14.3 102.03,13.82C101.45,13.34 100.15,12.91 99.62,12.76C99.09,12.67 97.7,12.67 97.22,12.91C96.74,13.15 96.21,14.25 96.21,14.5C96.21,14.74 96.21,15.12 96.21,15.12"
        android:fillColor="#FFFAFA"/>
  </group>
  <path
      android:pathData="M97.79,36.42L97.51,21.66C97.51,21.66 95.78,20.41 95.82,17.24C95.92,14.06 97.6,13.1 98.85,13.01C100.49,12.91 102.12,14.21 102.31,17.24C102.56,20.7 100.87,21.71 100.87,21.71L102.31,35.65L97.79,36.42Z"
      android:strokeWidth="0.992181"
      android:fillColor="#00000000"
      android:strokeColor="#E6B293"/>
  <path
      android:pathData="M82.99,79.12C82.84,79.21 96.21,81.86 105.34,80.56C113.95,79.31 116.64,77.1 117.99,71.52C119.33,65.75 118.08,62.48 117.36,57.77C116.6,53.06 118.52,40.7 115.1,36.13C110.73,30.31 100.1,30.94 95.1,36.18C90.1,41.42 91.59,49.11 90.73,54.55C89.91,59.64 87.7,63.06 87.46,68.78C87.17,74.98 84.67,77.96 82.99,79.12Z"
      android:fillColor="#F9E1D5"/>
  <group>
    <clip-path
        android:pathData="M82.99,79.12C82.84,79.21 96.21,81.86 105.34,80.56C113.95,79.31 116.64,77.1 117.99,71.52C119.33,65.75 118.08,62.48 117.36,57.77C116.6,53.06 118.52,40.7 115.1,36.13C110.73,30.31 100.1,30.94 95.1,36.18C90.1,41.42 91.59,49.11 90.73,54.55C89.91,59.64 87.7,63.06 87.46,68.78C87.17,74.98 84.67,77.96 82.99,79.12Z"/>
    <path
        android:pathData="M87.94,55.7C87.94,55.7 83.42,75.32 99.43,77.19C115.39,79.07 119.72,69.84 119.72,69.84C119.72,69.84 120.73,74.98 119.91,76.57C119.14,78.16 114.48,83.01 112.89,83.54C111.31,84.07 103.23,89.26 97.99,87.82C92.75,86.38 84.67,86.23 84.09,85.75C83.51,85.27 80.63,81.81 80.63,81.81C80.63,81.81 81.78,78.4 82.55,76.57C83.37,74.74 87.94,55.7 87.94,55.7Z"
        android:fillColor="#FFD0B8"/>
  </group>
  <group>
    <clip-path
        android:pathData="M82.99,79.12C82.84,79.21 96.21,81.86 105.34,80.56C113.95,79.31 116.64,77.1 117.99,71.52C119.33,65.75 118.08,62.48 117.36,57.77C116.6,53.06 118.52,40.7 115.1,36.13C110.73,30.31 100.1,30.94 95.1,36.18C90.1,41.42 91.59,49.11 90.73,54.55C89.91,59.64 87.7,63.06 87.46,68.78C87.17,74.98 84.67,77.96 82.99,79.12Z"/>
    <path
        android:pathData="M93.47,38.92C93.47,38.92 95.53,32.86 106.45,33.06C115.2,33.2 116.98,42.82 116.98,42.82C116.98,42.82 116.69,33.97 116.5,33.82C116.31,33.68 108.32,30.46 108.28,30.22C108.23,30.03 102.41,30.99 102.31,31.08C102.22,31.18 96.3,32.91 96.11,33.25C95.92,33.58 93.03,37.14 92.99,37.29C92.94,37.38 93.47,38.92 93.47,38.92Z"
        android:fillColor="#FFFAFA"/>
  </group>
  <path
      android:pathData="M82.89,79.21C82.89,79.21 93.71,81.43 103.9,80.8C111.93,80.32 113.23,79.12 115.73,76.23C118.13,73.44 119.38,68.54 117.32,57.82C116.4,53.11 118.37,40.65 114.96,36.08C110.58,30.27 99.96,30.89 94.96,36.13C89.96,41.37 91.83,53.73 89.86,58.83C88.66,61.95 87.65,65.9 87.12,71.95C86.64,76.71 84.57,78.06 82.89,79.21Z"
      android:strokeWidth="0.992181"
      android:fillColor="#00000000"
      android:strokeColor="#EBB697"/>
  <path
      android:pathData="M99.81,64.21C101.16,64.36 103.66,64.6 105.82,64.55C106.5,64.55 106.98,65.08 106.93,65.75C106.69,68.11 105.73,72.92 101.31,72.53C97.84,72.24 98.28,67.29 98.61,65.08C98.71,64.55 99.24,64.12 99.81,64.21Z"
      android:fillColor="#BF3232"/>
  <group>
    <clip-path
        android:pathData="M99.81,64.21C101.16,64.36 103.66,64.6 105.82,64.55C106.5,64.55 106.98,65.08 106.93,65.75C106.69,68.11 105.73,72.92 101.31,72.53C97.84,72.24 98.28,67.29 98.61,65.08C98.71,64.55 99.24,64.12 99.81,64.21Z"/>
    <path
        android:pathData="M97.99,68.88C98.18,68.83 104.04,68.73 105.1,73.16C106.16,77.58 99.33,73.73 98.71,73.11C98.08,72.48 96.98,70.9 97.07,70.13"
        android:fillColor="#FF9494"/>
  </group>
  <path
      android:pathData="M110.87,61.33C113.05,61.33 114.82,57.56 114.82,52.91C114.82,48.27 113.05,44.5 110.87,44.5C108.7,44.5 106.93,48.27 106.93,52.91C106.93,57.56 108.7,61.33 110.87,61.33Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M111.83,53.73C111.83,56.62 110.82,58.97 109.62,58.97C108.71,58.97 107.94,57.67 107.6,55.85C107.46,55.17 107.41,54.45 107.41,53.68C107.41,50.8 108.42,48.44 109.62,48.44C110.87,48.44 111.83,50.8 111.83,53.73Z"
      android:fillColor="#000000"/>
  <group>
    <clip-path
        android:pathData="M111.83,53.73C111.83,56.62 110.82,58.97 109.62,58.97C108.71,58.97 107.94,57.67 107.6,55.85C107.46,55.17 107.41,54.45 107.41,53.68C107.41,50.8 108.42,48.44 109.62,48.44C110.87,48.44 111.83,50.8 111.83,53.73Z"/>
    <path
        android:pathData="M107.27,51.42C107.27,51.42 110.54,49.69 111.98,54.26C113.42,58.83 113.37,49.79 112.75,49.45C112.07,49.11 110.73,48.87 110.3,48.44C109.86,48.01 108.81,48.25 108.66,48.25C108.57,48.2 107.27,51.33 107.27,51.33"
        android:fillColor="#473634"/>
  </group>
  <path
      android:pathData="M108.66,53.15C108.66,54.02 108.37,54.74 107.99,54.74C107.7,54.74 107.51,54.36 107.36,53.83C107.32,53.63 107.32,53.39 107.32,53.2C107.32,52.34 107.6,51.61 107.99,51.61C108.37,51.57 108.66,52.29 108.66,53.15Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M99.62,61.33C102.04,61.33 104,57.56 104,52.91C104,48.27 102.04,44.5 99.62,44.5C97.21,44.5 95.25,48.27 95.25,52.91C95.25,57.56 97.21,61.33 99.62,61.33Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M101.5,53.78C101.5,56.66 100.44,58.97 99.14,58.97C98.18,58.97 97.36,57.72 96.98,55.89C96.83,55.22 96.78,54.5 96.78,53.78C96.78,50.89 97.84,48.59 99.14,48.59C100.44,48.54 101.5,50.89 101.5,53.78Z"
      android:fillColor="#000000"/>
  <group>
    <clip-path
        android:pathData="M101.5,53.78C101.5,56.66 100.44,58.97 99.14,58.97C98.18,58.97 97.36,57.72 96.98,55.89C96.83,55.22 96.78,54.5 96.78,53.78C96.78,50.89 97.84,48.59 99.14,48.59C100.44,48.54 101.5,50.89 101.5,53.78Z"/>
    <path
        android:pathData="M96.64,51.52C96.64,51.52 100.1,49.84 101.64,54.36C103.18,58.88 103.13,49.93 102.46,49.6C101.79,49.26 100.29,49.02 99.86,48.59C99.38,48.15 98.28,48.39 98.13,48.39C97.99,48.35 96.69,51.42 96.69,51.42"
        android:fillColor="#473634"/>
  </group>
  <path
      android:pathData="M98.13,53.2C98.13,54.07 97.79,54.74 97.41,54.74C97.12,54.74 96.88,54.36 96.78,53.83C96.74,53.63 96.74,53.39 96.74,53.2C96.74,52.34 97.07,51.66 97.46,51.66C97.84,51.66 98.13,52.34 98.13,53.2Z"
      android:fillColor="#ffffff"/>
</vector>
