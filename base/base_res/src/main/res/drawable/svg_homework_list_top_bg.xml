<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="375dp"
    android:height="157dp"
    android:viewportWidth="375"
    android:viewportHeight="157">
  <group>
    <clip-path
        android:pathData="M0,0h375v157h-375z"/>
    <path
        android:pathData="M0,0H375V111.88C375,131.72 375,141.64 369.49,148.3C368.53,149.46 367.46,150.53 366.3,151.49C359.64,157 349.72,157 329.88,157H45.12C25.28,157 15.36,157 8.7,151.49C7.54,150.53 6.47,149.46 5.51,148.3C0,141.64 0,131.72 0,111.88V0Z"
        android:fillColor="#B4E4FF"/>
    <path
        android:pathData="M253.68,33L303.5,33A18.68,18.68 0,0 1,322.19 51.68L322.19,113.96A18.68,18.68 0,0 1,303.5 132.64L253.68,132.64A18.68,18.68 0,0 1,235 113.96L235,51.68A18.68,18.68 0,0 1,253.68 33z"
        android:strokeAlpha="0.6"
        android:fillAlpha="0.6">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="278.59"
            android:startY="33"
            android:endX="278.59"
            android:endY="132.64"
            android:type="linear">
          <item android:offset="0" android:color="#FFD4EFFF"/>
          <item android:offset="1" android:color="#99D4EFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M250.73,59.88C250.73,57.16 252.93,54.96 255.65,54.96H301.54C304.25,54.96 306.45,57.16 306.45,59.88C306.45,62.59 304.25,64.79 301.54,64.79H255.65C252.93,64.79 250.73,62.59 250.73,59.88Z"
        android:fillColor="#E9F7FF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M250.73,82.82C250.73,80.11 252.93,77.9 255.65,77.9H301.54C304.25,77.9 306.45,80.11 306.45,82.82C306.45,85.54 304.25,87.74 301.54,87.74H255.65C252.93,87.74 250.73,85.54 250.73,82.82Z"
        android:fillColor="#E9F7FF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M250.73,105.76C250.73,103.05 252.93,100.85 255.65,100.85H301.54C304.25,100.85 306.45,103.05 306.45,105.76C306.45,108.48 304.25,110.68 301.54,110.68H255.65C252.93,110.68 250.73,108.48 250.73,105.76Z"
        android:fillColor="#E9F7FF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M269.51,44.39L319.33,44.39A18.68,18.68 0,0 1,338.01 63.07L338.01,125.35A18.68,18.68 0,0 1,319.33 144.03L269.51,144.03A18.68,18.68 0,0 1,250.82 125.35L250.82,63.07A18.68,18.68 0,0 1,269.51 44.39z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="294.42"
            android:startY="44.39"
            android:endX="294.42"
            android:endY="144.03"
            android:type="linear">
          <item android:offset="0" android:color="#FFD4EFFF"/>
          <item android:offset="1" android:color="#FFC6EAFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M266.56,71.27C266.56,68.55 268.76,66.35 271.47,66.35H317.36C320.08,66.35 322.28,68.55 322.28,71.27C322.28,73.98 320.08,76.18 317.36,76.18H271.47C268.76,76.18 266.56,73.98 266.56,71.27Z"
        android:fillColor="#E9F7FF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M266.56,91.1C266.56,88.38 268.76,86.18 271.47,86.18H317.36C320.08,86.18 322.28,88.38 322.28,91.1C322.28,93.82 320.08,96.02 317.36,96.02H271.47C268.76,96.02 266.56,93.82 266.56,91.1Z"
        android:fillColor="#E9F7FF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M266.56,110.93C266.56,108.22 268.76,106.02 271.47,106.02H317.36C320.08,106.02 322.28,108.22 322.28,110.93C322.28,113.65 320.08,115.85 317.36,115.85H271.47C268.76,115.85 266.56,113.65 266.56,110.93Z"
        android:fillColor="#E9F7FF"
        android:fillType="evenOdd"/>
  </group>
</vector>
