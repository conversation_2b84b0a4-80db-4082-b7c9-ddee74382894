{"assets": [{"h": 128, "id": "0", "p": "data:image/png;base64,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", "u": "", "w": 128, "e": 1}, {"id": "6", "layers": [{"ind": 5, "ty": 2, "parent": 4, "ks": {}, "ip": 0, "op": 96.4, "st": 0, "refId": "0"}, {"ind": 4, "ty": 3, "ks": {"s": {"a": 0, "k": [50, 50]}}, "ip": 0, "op": 96.4, "st": 0}]}], "fr": 60, "h": 64, "ip": 0, "layers": [{"ind": 8, "ty": 0, "parent": 3, "ks": {}, "w": 64, "h": 64, "ip": 0, "op": 96.4, "st": 0, "refId": "6"}, {"ind": 3, "ty": 3, "parent": 2, "ks": {"a": {"a": 0, "k": [32, 32]}, "p": {"a": 0, "k": [32, 32]}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 95.4, "s": [357.75], "h": 1}]}}, "ip": 0, "op": 96.4, "st": 0}, {"ind": 2, "ty": 3, "parent": 1, "ks": {}, "ip": 0, "op": 96.4, "st": 0}, {"ind": 9, "ty": 4, "parent": 1, "ks": {}, "ip": 0, "op": 96.4, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [32, 32]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [64, 64]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1]}, "o": {"a": 0, "k": 100}}]}, {"ind": 1, "ty": 3, "ks": {}, "ip": 0, "op": 96.4, "st": 0}], "meta": {"g": "https://jitter.video"}, "op": 95.4, "v": "5.7.4", "w": 64}