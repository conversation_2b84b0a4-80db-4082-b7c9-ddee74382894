package com.snails.base.router

import org.junit.Assert.assertEquals
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import kotlin.reflect.full.declaredFunctions
import kotlin.reflect.jvm.isAccessible

/**
 * @Description RouterManager 单元测试
 * <AUTHOR>
 * @CreateTime 2024年08月12日 17:17:12
 */
@RunWith(RobolectricTestRunner::class)
class HRouterTest {

    @Test
    fun testInterceptionPath_withSecondaryPath() {
        // 通过反射访问私有方法
        val method = HRouter::class.declaredFunctions
            .first { it.name == "interceptionPath" }
        method.isAccessible = true

        // 测试有二级路径的情况
        val originalUrl = "https://example.com/login/home"
        val expectedUrl = "https://example.com/login?router_jump=home"

        // 调用私有方法
        val result = method.call(HRouter, originalUrl) as String
        assertEquals(expectedUrl, result)
    }

    @Test
    fun testInterceptionPath_withoutSecondaryPath() {
        val method = HRouter::class.declaredFunctions
            .first { it.name == "interceptionPath" }
        method.isAccessible = true

        val originalUrl = "https://example.com/login"

        val result = method.call(HRouter, originalUrl) as String
        assertEquals(originalUrl, result)
    }

    @Test
    fun testInterceptionPath_withQueryParameters() {
        val method = HRouter::class.declaredFunctions
            .first { it.name == "interceptionPath" }
        method.isAccessible = true

        val originalUrl = "https://example.com/login/home?param=value"
        val expectedUrl = "https://example.com/login?param=value&router_jump=home"

        val result = method.call(HRouter, originalUrl) as String
        assertEquals(expectedUrl, result)
    }

    @Test
    fun testInterceptionPath_withEmptyPath() {
        val method = HRouter::class.declaredFunctions
            .first { it.name == "interceptionPath" }
        method.isAccessible = true

        val originalUrl = "https://example.com/"

        val result = method.call(HRouter, originalUrl) as String
        assertEquals(originalUrl, result)
    }

    @Test
    fun testInterceptionPath_withRootPath() {
        val method = HRouter::class.declaredFunctions
            .first { it.name == "interceptionPath" }
        method.isAccessible = true

        val originalUrl = "https://example.com"

        val result = method.call(HRouter, originalUrl) as String
        assertEquals(originalUrl, result)
    }
}