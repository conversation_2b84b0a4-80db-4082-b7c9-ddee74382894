package com.snails.base.network.repository.upload

import okhttp3.MediaType
import okhttp3.RequestBody
import okio.BufferedSink
import java.io.File

class ProgressRequestBody(
    private val file: File,
    private val contentType: MediaType?,
    private val listener: ProgressListener
) : RequestBody() {

    override fun contentType() = contentType
    override fun contentLength() = file.length()

    override fun writeTo(sink: BufferedSink) {
        runCatching {
            val buffer = ByteArray(8192)
            file.inputStream().use { input ->
                var uploaded = 0L
                var read: Int
                val total = contentLength()
                var lastCallbackTime = 0L
                var lastPercent = -1
                while (input.read(buffer).also { read = it } != -1) {
                    sink.write(buffer, 0, read)
                    uploaded += read
                    val percent = (100 * uploaded / total).toInt()
                    val now = System.currentTimeMillis()
                    if ((now - lastCallbackTime >= 1000 || uploaded == total) && percent != lastPercent) {
                        listener.onProgress(percent, uploaded, total)
                        lastCallbackTime = now
                        lastPercent = percent
                    }
                }
            }
        }
    }
}