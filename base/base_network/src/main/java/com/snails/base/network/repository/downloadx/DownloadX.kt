package com.snails.base.network.repository.downloadx

import com.snails.base.network.repository.downloadx.core.BatchDownloadListener
import com.snails.base.network.repository.downloadx.core.DownloadConfig
import com.snails.base.network.repository.downloadx.core.DownloadParam
import com.snails.base.network.repository.downloadx.core.DownloadTask
import com.snails.base.network.repository.downloadx.helper.Default
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

/**
 * DownloadX - 基于协程的下载库扩展函数
 *
 * 提供多种下载方式：
 * - 单文件下载
 * - 批量下载（支持进度聚合和状态监听）
 * - 支持自定义保存路径和下载配置
 *
 * 基于项目: https://github.com/ssseasonnn/DownloadX
 */

// ================================ 单文件下载 ================================

/**
 * 下载单个文件
 *
 * @param url 下载链接
 * @param saveName 保存文件名，为空时自动从URL提取
 * @param savePath 保存路径，默认使用系统默认路径
 * @param downloadConfig 下载配置，包含线程数、超时等设置
 * @param autoStartDownload 是否自动开始下载
 * @return DownloadTask 下载任务对象，可用于控制下载状态
 *
 */
fun CoroutineScope.download(
    url: String,
    saveName: String = "",
    savePath: String = Default.DEFAULT_SAVE_PATH,
    downloadConfig: DownloadConfig = DownloadConfig(),
    autoStartDownload: Boolean? = true
): DownloadTask {
    val downloadParam = DownloadParam(url, saveName, savePath)
    return createAndAddTask(downloadParam, downloadConfig).apply {
        if (autoStartDownload == true) {
            start()
        }
    }
}

/**
 * 使用DownloadParam对象下载单个文件
 *
 * @param downloadParam 下载参数对象，包含URL、文件名、路径等信息
 * @param downloadConfig 下载配置
 * @return DownloadTask 下载任务对象
 */
fun CoroutineScope.customDownload(
    downloadParam: DownloadParam,
    downloadConfig: DownloadConfig = DownloadConfig(),
    autoStartDownload: Boolean? = true
): DownloadTask {
    return createAndAddTask(downloadParam, downloadConfig).apply {
        if (autoStartDownload == true) {
            start()
        }
    }
}

// ================================ 批量下载（无监听） ================================

/**
 * 批量下载文件（List版本）
 *
 * @param urls 下载链接列表
 * @param savePath 保存路径，所有文件保存到同一目录
 * @param downloadConfig 下载配置
 * @return List<DownloadTask> 下载任务列表
 */
fun CoroutineScope.download(
    urls: List<String>,
    savePath: String = Default.DEFAULT_SAVE_PATH,
    downloadConfig: DownloadConfig = DownloadConfig(),
    autoStartDownload: Boolean? = true
): List<DownloadTask> {
    return createTasksFromUrls(urls, savePath, downloadConfig).apply {
        if (autoStartDownload == true) {
            this.forEach {
                it.start()
            }
        }
    }
}

/**
 * 批量下载文件（Set版本）
 *
 * @param urls 下载链接集合（自动去重）
 * @param savePath 保存路径，所有文件保存到同一目录
 * @param downloadConfig 下载配置
 * @return List<DownloadTask> 下载任务列表
 */
fun CoroutineScope.download(
    urls: Set<String>,
    savePath: String = Default.DEFAULT_SAVE_PATH,
    downloadConfig: DownloadConfig = DownloadConfig(),
    autoStartDownload: Boolean? = true
): List<DownloadTask> {
    return createTasksFromUrls(urls.toList(), savePath, downloadConfig).apply {
        if (autoStartDownload == true) {
            this.forEach {
                it.start()
            }
        }
    }
}

// ================================ 批量下载（带监听） ================================

/**
 * 批量下载文件并监听整体进度
 *
 * @param params 下载参数列表，每个参数包含URL、文件名、路径等信息
 * @param downloadConfig 下载配置
 * @param listener 批量下载监听器，用于接收整体进度和完成状态
 * @return List<DownloadTask> 下载任务列表
 */
fun CoroutineScope.download(
    params: List<DownloadParam>,
    downloadConfig: DownloadConfig = DownloadConfig(),
    listener: BatchDownloadListener,
    autoStartDownload: Boolean? = true
): List<DownloadTask> {
    val tasks = createTasksFromParams(params, downloadConfig)
    startBatchDownloadWithListener(tasks, listener)
    return tasks.apply {
        if (autoStartDownload == true) {
            this.forEach {
                it.start()
            }
        }
    }
}

/**
 * 批量下载文件并监听整体进度（URL集合版本）
 *
 * @param urls 下载链接集合
 * @param downloadConfig 下载配置
 * @param listener 批量下载监听器
 * @return List<DownloadTask> 下载任务列表
 */
fun CoroutineScope.downloadBatch(
    urls: Set<String>,
    downloadConfig: DownloadConfig = DownloadConfig(),
    listener: BatchDownloadListener,
    autoStartDownload: Boolean? = true
): List<DownloadTask> {
    val params = urls.map { DownloadParam(url = it) }
    val tasks = createTasksFromParams(params, downloadConfig)
    startBatchDownloadWithListener(tasks, listener)
    return tasks.apply {
        if (autoStartDownload == true) {
            this.forEach {
                it.start()
            }
        }
    }
}

// ================================ 私有辅助函数 ================================

/**
 * 创建并添加单个下载任务到任务管理器
 */
private fun CoroutineScope.createAndAddTask(
    downloadParam: DownloadParam,
    downloadConfig: DownloadConfig
): DownloadTask {
    val task = DownloadTask(this, downloadParam, downloadConfig)
    return downloadConfig.taskManager.add(task)
}

/**
 * 从URL列表创建下载任务列表
 */
private fun CoroutineScope.createTasksFromUrls(
    urls: List<String>,
    savePath: String,
    downloadConfig: DownloadConfig
): List<DownloadTask> {
    return urls.map { url ->
        val param = DownloadParam(url, "", savePath)
        createAndAddTask(param, downloadConfig)
    }
}

/**
 * 从DownloadParam列表创建下载任务列表
 */
private fun CoroutineScope.createTasksFromParams(
    params: List<DownloadParam>,
    downloadConfig: DownloadConfig
): List<DownloadTask> {
    return params.map { param ->
        createAndAddTask(param, downloadConfig)
    }
}

/**
 * 启动批量下载并设置监听器
 *
 * 功能包括：
 * 1. 启动所有下载任务
 * 2. 聚合所有任务的下载进度
 * 3. 监听所有任务的完成状态
 */
private fun CoroutineScope.startBatchDownloadWithListener(
    tasks: List<DownloadTask>,
    listener: BatchDownloadListener
) {
    // 启动所有任务
    tasks.forEach { it.start() }

    // 聚合进度监听
    launch {
        combine(tasks.map { it.progress() }) { progresses ->
            val overall = progresses.map { it.percent() }.average()
            listener.onProgress((overall * 100).toInt(), tasks.zip(progresses))
        }.collect()
    }

    // 聚合完成/失败状态监听
    launch {
        while (true) {
            val allDone = tasks.all { it.isSucceed() || it.isFailed() }
            if (allDone) {
                val failed = tasks.filter { it.isFailed() }
                if (failed.isEmpty()) {
                    listener.onComplete(tasks)
                } else {
                    listener.onError(failed)
                }
                break
            }
            delay(200) // 每200ms检查一次状态
        }
    }
}