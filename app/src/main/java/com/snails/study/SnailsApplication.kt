package com.snails.study

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.os.PowerManager
import android.view.WindowManager
import com.sanils.base.log.HLog
import com.snails.base.storage.Storage

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2024年08月13日 15:39:07
 */
class SnailsApplication : Application() {

    private var wakeLock: PowerManager.WakeLock? = null

    override fun onCreate() {
        super.onCreate()
        Storage.init(this)
        HLog.initLog()
        openScreenAlwaysOn()
    }

    /**
     * 低内存的时候执行
     */
    override fun onLowMemory() {
        HLog.close()
        super.onLowMemory()
    }

    /**
     * 程序终止的时候执行
     * 不保证能被调用
     */
    override fun onTerminate() {
        HLog.close()
        wakeLock?.release()
        super.onTerminate()
    }

    /**
     * 打开屏幕常亮
     */
    @SuppressLint("WakelockTimeout")
    private fun openScreenAlwaysOn() {
        val powerManager = getSystemService(Context.POWER_SERVICE) as? PowerManager
        wakeLock = powerManager?.newWakeLock(
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
            "Snails::WakeLockTag"
        )
        wakeLock?.acquire()
    }
}